{"name": "galaxy2", "version": "1.2.20", "group": "trip", "private": true, "description": "星辰2", "author": {"name": "祁南", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "http://gitlab.alibaba-inc.com/trip/galaxy2"}, "keywords": ["clam", "galaxy2"], "toolkit": "@ali/clam-toolkit-one", "commandType": "clam", "defPublishType": "assets", "isCloudBuild": true, "scripts": {"start": "NODE_OPTIONS=--openssl-legacy-provider umi dev", "build": "NODE_OPTIONS=--openssl-legacy-provider umi build", "analyze": "ANALYZE=1 umi build", "postinstall": "umi generate tmp", "fix": "umi lint --style --prettier --eslint.ext='.ts,.tsx,.jsx,.js' --fix"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ali/alitrip-mtop": "^1.19.0", "@ali/umi-plugin-find-code": "^0.2.4", "@ali/umi-preset-whale": "1.x", "@ali/use-global": "1.x", "@ant-design/icons": "^4.5.0", "@ant-design/pro-layout": "^6.5.0", "antd": "^4.16.12", "braft-editor": "^2.3.9", "classnames": "^2.2.6", "dayjs": "1.10.5", "form-render": "^1.6.4", "lodash": "^4.17.21", "react-fast-marquee": "^1.2.1", "react-monaco-editor": "^0.49.0", "table-render": "1.x", "umi": "^3.5.17", "umi-request": "^1.3.5"}, "devDependencies": {"@types/lodash": "^4.14.172", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "@umijs/plugin-blocks": "^2.2.2", "@umijs/plugin-esbuild": "^1.1.0", "@umijs/plugin-qiankun": "^2.25.0", "@umijs/preset-ant-design-pro": "^1.3.0", "@umijs/preset-react": "^1.7.0", "babel-plugin-import": "^1.13.3", "eslint-plugin-prettier": "^3.4.0", "prettier": "^2.2.1", "react": "17.x", "react-dom": "17.x", "typescript": "^4.1.2"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"], "__npminstall_done": false}