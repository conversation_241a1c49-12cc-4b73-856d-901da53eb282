import MTop from '@ali/alitrip-mtop';
import { extend } from 'umi-request';

//用于 http api 请求的场景
const req = extend({
  // prefix: '/api',         // 假如需要统一加前缀的参加
  credentials: 'include', // 默认请求是否带上cookie
  // headers: {
  //   'Content-Type': 'multipart/form-data',
  // },
});

//用于 http 接口请求的场景
export const runApi = async (api: string, params?: any, method = 'get') => {
  const options: any = { method };
  if (method.toUpperCase() === 'GET') {
    options.params = params;
  }
  if (method.toUpperCase() === 'POST') {
    options.data = params;
  }
  return req(api, options)
    .then((res:any) => res)
    .catch((err:any) => {
      // TODO: 发布了记得去掉
      console.group(api);
      console.log('%cParams:', 'color: #FF4D4F; font-weight: 700;', params);
      console.log('%cResponse:', 'color: #FF4D4F; font-weight: 700;', err);
      console.groupEnd();
    });
};

//用于http接口转发的场景
export interface ProxyParams {
  url: string;
  method: string;
  params: object;
}

export const mtopProxy = (proxyParams: ProxyParams) => {
  const options = {
    api: 'mtop.fliggy.serverless.fc.api',
    version: '1.0',
    data: {
      fcGroup: 'fl-mtop',
      fcName: 'index',
      fcArgs: JSON.stringify(proxyParams),
    },
  };
  return MTop.request(options);
};

//用于http接口转发使用mtop请求的场景
export const runMtopProxy = async (api: string, params?: any, method = 'get') => {
  return mtopProxy({ url: api, params, method })
    .then((res:any) => res.data)
    .catch((err:any) => {
      // TODO: 发布了记得去掉
      console.group(api);
      console.log('%cParams:', 'color: #FF4D4F; font-weight: 700;', params);
      console.log('%cResponse:', 'color: #FF4D4F; font-weight: 700;', err);
      console.groupEnd();
    });
};
