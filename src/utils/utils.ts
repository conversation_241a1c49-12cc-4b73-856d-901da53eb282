/**
 * 获取ULR中的参数
 * @param {str} 有则解析字符串中的参数，无则取url中的
 * @return {Objct} url中以k-v形式的的参数对象
 */
export const getUrlParams: (str?: string) => any = function (str?: string) {
  let results = {} as any,
    hash;
  const loc = str || window.location.hash;
  if (loc.indexOf('?') == -1 || !loc.split('?')[1].length) {
    return {};
  }
  let params = loc.slice(loc.indexOf('?') + 1).split('&');

  // console.log('params', params);
  for (let i = 0; i < params.length; i++) {
    hash = params[i].split('=');
    results[hash[0]] = hash[1];
  }
  return results;
};

interface urlParam {
  params?: any;
  deleteKeys?: string[];
  url?: string;
}

/**
 * 修改url中的Search
 * @param params 新的参数
 * @param url 原始的url
 */
export const updateSearch = ({ params, deleteKeys, url }: urlParam) => {
  const temp = url || window.location.href;
  const originParams = getUrlParams(temp);
  const newParams = { ...originParams, ...(params || {}) };
  if (deleteKeys && deleteKeys.length > 0) {
    deleteKeys.forEach((element: string) => {
      delete newParams[element];
    });
  }
  const newSearch = Object.keys(newParams).reduce((acc, cur) => {
    return `${acc}&${cur}=${newParams[cur]}`;
  }, '');
  return newSearch.slice(1);
};

export const getNewHash = (hashParam: urlParam) => {
  const newParam = updateSearch(hashParam);
  // console.log('newParam', newParam);
  const originHash = window.location.hash;
  const basePath = originHash.split('?')[0];
  // console.log('basePath', basePath);
  // console.log(`${basePath}?${newParam}`);
  return `${basePath}?${newParam}`;
};

export const updateHash = (hashParam: urlParam) => {
  // const newHash = getNewHash(hashParam);
  // console.log('newHash', newHash);
  // console.log(window.location.href.split('#')[0]);
  window.location.hash = getNewHash(hashParam);
};

export const getReactKey = () => {
  return `${new Date().getTime()}`;
};


export const copyToClipboard = (text:any) => {
  var textField = document.createElement('textarea')
  textField.value = text
  document.body.appendChild(textField)
  textField.select()
  document.execCommand('copy')
  textField.remove()
}

/**
 * 去除重复的值
 * @param arr 原数组
 * @param keyname 以那个字段去重
 */
export const unique = (arr:any,keyname:string) => {
  const temp:any = []
  const obj:any = {}
  arr.forEach((el:any) => {
    if(!obj[el[keyname]]) {
      temp.push(el)
      obj[el[keyname]] = true
    }
  })

  return temp
}

/**
 * 过滤商品
 */
export const filterCheck = (reasonEnum: string) => {
  if(reasonEnum === 'NO_ITEM') {
    return 'Summary没有该商品'
  }

  if(reasonEnum === 'OFF_SHELF') {
    return '商品已下架'
  }

  if(reasonEnum === 'EXPIRED') {
    return '商品已过期'
  }

  if(reasonEnum === 'TEST') {
    return '测试商品'
  }

  if(reasonEnum === 'NO_PRICE') {
    return '商品/酒店无价格 - 自查一下是不是未来可售的酒店'
  }

  if(reasonEnum === 'BLACK') {
    return '商品被剔除在黑名单里'
  }
}

/**
 * 过滤商品显示
 */

 export const filterCheckName = (type: string) => {
  if (type === 'SHOP') {
    return '商家'
  }

  if (type === 'BNB') {
    return '民宿'
  }

  if (type === 'SPU') {
    return 'SPU'
  }

  return '商品'
}

/**
 * 获取Cascader最后一项，得到整个完整数据
 */


 export const getVal = (data, id, indexArray) => {
  let arr = Array.from(indexArray);
  for (let i = 0, len = data.length; i < len; i++) {
    arr.push(data[i].id);
    if (data[i].id === id) {
      return arr;
    }
    let childNode = data[i].childNode;
    if (childNode && childNode.length) {
      let result = getVal(childNode, id, arr);
      if (result) return result;
    }
    arr.pop();
  }
  return false;
};


// 活动类型
export const getActiveType = (type:any) => {
  switch (type) {
    case 'HOTEL':
      return '酒店'
    case 'TRAVEL':
      return '商品'
    case 'POI':
      return 'POI'
    case 'SHOP':
      return '商家'
    case 'BNB':
      return '民宿'
    case 'GLB':
      return '哥伦布榜单'
    case 'FLIGHT':
      return '机票'
    default:
      return '暂无'
  }
}


//选品池状态

export const poolStatus = (status:any) => {
  switch (status) {
    case 0:
      return {text: '无效', color: '#abbcc3'}
    case 1:
      return {text: '已完成', color: '#87d068'}
    case 2:
      return {text: '停用', color: '#ff5500'}
    case 3:
      return {text: '待发布', color: '#0f8ee9'}
    case 4:
      return {text: '发布中', color: '#f17e10'}
    case 5:
      return {text: '发布失败', color: '#ff4d4f'}
    default:
      return {text: '无效', color: '#abbcc3'}
  }
}


// 哥伦布预览榜单类型
export const getGLBType = (type:any) => {
  switch (type) {
    case '1':
      return '单榜'
    case '2':
      return '总榜'
    case '3':
      return '总榜tab'
    default:
      return '单榜'
  }
}