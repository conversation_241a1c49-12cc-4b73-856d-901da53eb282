import api from '@/api';
import { message } from 'antd';

export const downloadTemp = () => {
  const fileName = '1639537051283-%E6%98%9F%E8%BE%B0%E6%A8%A1%E7%89%88.xlsx';
  api.activity.downloadTemp({ file: fileName }).then((res) => {
    const filePath = res?.data;
    if (filePath) {
      const a = document.createElement('a'); 
      a.download = fileName;
      a.href = filePath;
      document.body.appendChild(a);
      a.click(); // 触发a标签的click事件
      document.body.removeChild(a);
      return;
    }
    message.error('下载上传模板失败');
  }).catch((err: any) => {
    message.error(err?.msg || '下载上传模板失败');
  });
}