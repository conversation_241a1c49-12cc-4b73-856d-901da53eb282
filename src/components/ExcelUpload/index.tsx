import React, { useState, useEffect } from 'react';
import _ from 'lodash';
import { message,Upload,Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

const ExcelUpload = (props:any) => {
  const { value, onChange, name } = props;
  const [loading, setLoading] = useState(false);

  const beforeUpload = (file:any) => {
    const isExcel = file.name.split('.').slice(-1)[0] === 'xlsx';
    const isExl = file.name.split('.').slice(-1)[0] === 'xls';

    if (!isExcel && !isExl) {
      message.error('请上传xlsx 或 xls类型的文件!');
      return Upload.LIST_IGNORE;
    }
  };

  const handleChange = (info:any) => {
    console.log('info===',info)
    if (info.file.status === 'uploading') {
      setLoading(true)
      return;
    }
    if (info.file.status === 'done') {
      setLoading(false)
      if (_.get(info, 'file.response.code', false) === '200') {
        onChange(_.get(info, 'file.response.data.data', ''))
        message.success('上传成功')
      } else {
        message.error(_.get(info, 'file.response.msgInfo', '上传失败'))
      }
    }
    if (info.file.status === 'error') {
      message.error('上传失败')
      setLoading(false)
      onChange('')
    }
  }

  const onRemove = (file) => {
    onChange('')
  }

  return (
    <div style={{flexDirection:'column',width:'100%'}}>
      <Upload
        name="pic"
        action="/activity/UploadPic"
        beforeUpload={beforeUpload}
        onChange={handleChange}
        onRemove={onRemove}
        maxCount={1}
      >
        <Button icon={<UploadOutlined />} loading={loading}>
          选择Excel文件
        </Button>
      </Upload>
    </div>
  );
};

export default ExcelUpload;
