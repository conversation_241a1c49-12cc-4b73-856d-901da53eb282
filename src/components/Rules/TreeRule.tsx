import { useState, useEffect } from 'react';
import { TreeSelect } from 'antd';

import api from '@/api';

/**
 * 规则类型——地区选择
 */
const AREASELECT = 'areaSelect';
const BNBCITY = 'bnb_city'

const TreeRule = (props: { rule: any; onChange: any }) => {
  const { rule, onChange } = props;

  const [treeData, setTreeData] = useState<any>();

  let value;
  if (treeData) {
    if (rule.ruleName === AREASELECT || rule.ruleName === BNBCITY) {
      if (rule.ruleValue) {
        value = rule.ruleValue.split(',').map((ele: any) => {
          return ele.replaceAll('city#', '');
        });
      } else {
        value = [];
      }
    } else {
      value = rule.ruleValue ? rule.ruleValue.split(',').map((e) => e) : [];
    }
  }

  const addLayer = (node: any) => {
    const setLayer = (node: any, layer: number) => {
      node.forEach((element: any) => {
        element.layer = layer;
        if (element.children) {
          setLayer(element.children, layer + 1);
        }
      });
    };

    setLayer(node, 1);

    return node;
  };

  const formateTreeData = (node: any) => {
    if (node.children && node.children.length > 0) {
      return {
        title: node.content || node.name,
        value: `${node.layer ? layp(node.layer) : ''}${getLeafChildrenValue(node.children).join(',')}`,
        leaf: node.leaf || node.leafPlay,
        children: node.children.map(formateTreeData),
        layer: node.layer,
      };
    }
    return {
      title: node.content || node.name,
      value: `${node.id}`,
      sumbitValue: node.id,
      leaf: node.leaf || node.leafPlay,
      layer: node.layer,
    };
  };

  /**
   * 给父级value增加p-，用于区分选中的key
   * @param layer 层数
   * @returns
   */
  const layp = (layer: number) => {
    let temp = '';
    for (var i = 0; i < layer; i++) {
      temp += 'p-';
    }
    return temp;
  };

  const getLeafChildrenValue = (array: any, layer: number = 0) => {
    let values: any[] = [];
    array.forEach((item: any) => {
      // 如果是叶子，则把值放到数组中
      if (item.isleafCat || item.leaf || item.leafPlay || (item.children && item.children.length === 0)) {
        values.push(item.treeType ? `${item.treeType}#${item.id}` : item.id);
      } else if (item.children) {
        // 如果有chilren，
        item.children.forEach((child: any) => {
          // 修改bug item.leaf => child.leaf
          if (child.isleafCat || child.leaf || child.leafPlay) {
            values.push(child.treeType ? `${child.treeType}#${child.id}` : child.id);
          } else if (child.children) {
            values = values.concat(getLeafChildrenValue(child.children, layer++));
          }
        });
      }
    });

    return values;
  };

  useEffect(() => {
    let data:any = {}
    if (rule.menuName) {
      data = {
        id: rule.menuName,
      };
    } else {
      data = {
        id: rule.ruleName,
      };
      if (rule.ruleName === 'd2gjspyzlmxsj') {
        data.id = 'categoryId';
      }
  
      if (rule.ruleName === 'poiTitlePlayTag') {
        data.id = 'poiTitlePlayTag';
      }
      if (rule.ruleName === 'playTagRelScore') {
        data.id = 'playTagId';
      }
      if (rule.ruleName === 'sellerMainCateScoreParent') {
        data.id = 'sellerMainCate';
      }
      if (rule.ruleName === 'poiTagRelScore') {
        data.id = 'poiTagIdTree';
      }
  
      if (rule.ruleName === 'jiujingPackagePOI') {
        data.id = 'poiTitlePlayTag';
      }
    }
   

    api.ruleType.getTreeData(data).then((res: any) => {
      // console.log('res', res.data);
      const temp = addLayer(res.data).map(formateTreeData);
      setTreeData(temp);
    });
  }, []);

  return (
    <TreeSelect
      value={value}
      showCheckedStrategy={TreeSelect.SHOW_PARENT}
      getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
      treeData={treeData}
      treeNodeFilterProp="title"
      onChange={(value: string[], labelList) => {
        const temp = value.map((e) => {
          // 去掉所有的p-
          return e?.toString().replaceAll('p-', '');
        });
        if (rule.ruleName === AREASELECT || rule.ruleName === BNBCITY) {
          let newTemp = temp
            .join(',')
            .split(',')
            .map((ele) => {
              if (ele) {
                if (ele.includes('city#')) {
                  return ele
                }
                return `city#${ele}`;
              }
              return undefined
            });
          onChange(newTemp.join(','));
        } else {
          onChange(temp.join(','));
        }
      }}
      treeCheckable
      placeholder="请选择"
      style={{ width: 300 }}
    />
  );
};

export default TreeRule;
