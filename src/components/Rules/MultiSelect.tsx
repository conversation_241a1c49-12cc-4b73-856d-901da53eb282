import { useState, useEffect } from 'react';
import { Select } from 'antd';

const { Option } = Select;

interface dataType {
  content?: string;
  id: string;
}

import api from '@/api';

const Serious = (props: any) =>{
  
  const [dataList, setDataList] = useState([])

  const { onChange, rule } = props;

  useEffect(() => {
    let data:any = {}
    if (rule.menuName) {
      data = {
        id: rule.menuName,
      }
    }
    
    api.ruleType.getTreeData(data).then((res: any) => {
      setDataList(res?.data)
    });
  }, []);

  let value:any = [];
  if (rule.ruleValue) {
    value = rule?.ruleValue?.split(',')
  } else {
    value = []
  }

  const handleChange = (changeValue:any) => {
    onChange(changeValue?.join(','))
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Select
        optionFilterProp="children"
        mode="multiple"
        allowClear
        onChange={handleChange}
        style={{ width: 320 }}
        value={value}
      >
        {dataList && dataList.length > 0 && dataList.map((ele:dataType) => (
          <Option value={ele.id}>{ele.content}</Option>
        ))}
      </Select>
    </div>
  );
};

export default Serious;