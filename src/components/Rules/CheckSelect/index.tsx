import { useState, useEffect } from 'react';
import { Select } from 'antd';

import api from '@/api';

import styles from './index.less';

const Option = Select.Option;

const CheckSelect = (props: any) => {
  const [selectOne, setSelectOne] = useState<any>([]);
  const [selectTwo, setSelectTwo] = useState<any>([]);
  const [firstValue, setFirstValue] = useState<any>('');

  const { onChange, record } = props;

  const domType = record.ruleDom;

  useEffect(() => {
    let params = '';
    if (domType === 'doubleSelect') {
      params = 'type=holidayCategoryId';
    } else if (domType === 'linePlayLabel') {
      params = 'type=getCategoryList';
    }
    api.ruleType.doubleSelect(params).then((res: any) => {
      if (res.data) {
        setSelectOne(res.data);
      }
    });

  }, []);



  const cascOneChange = (value: any, record: any) => {
    if (record.ruleValue) {
      record.ruleValue = [];
    }

    let params = '';

    if (domType === 'doubleSelect') {
      params = `type=holidayTopicPlay&firstValue=${value}`;
    } else if (domType === 'linePlayLabel') {
      params = `type=getLineWayPlay&provinceName=${value}`;
    }

    api.ruleType.doubleSelect(params).then((res: any) => {
      if (res.data) {
        setSelectTwo(res.data);
      }
    });
    setFirstValue(value);
  };

  const cascTwoChange = (value: any) => {
    onChange(value.join(','));
  };

  const nameType = record.valueTemplate.split('%s');

  const secondValue = (val:any) => {
    if (val) {
      if (val.length > 0) {
        return record.ruleValue.split(',')
      } else {
        return []
      }
    } else {
      return []
    }
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center' }} className={styles.checkBox}>
      <div style={{ marginRight: '10px' }}>
        <span style={{ marginRight: '10px' }}>{nameType[0]}</span>
        <Select
          style={{ width: 120 }}
          value={firstValue}
          onChange={(e) => cascOneChange(e, record)}
          getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
        >
          {selectOne &&
            selectOne.map((item: any) => (
              <Option value={item.id} key={item.id}>
                {item.content}
              </Option>
            ))}
        </Select>
      </div>
      {firstValue ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: '10px' }}>{nameType[1]}</span>
          <Select
            style={{ width: 200 }}
            value={secondValue(record.ruleValue)}
            onChange={(e) => cascTwoChange(e)}
            mode="multiple"
            getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
          >
            {selectTwo &&
              selectTwo.map((item: any) => (
                <Option value={item.id} key={item.id}>
                  {item.content}
                </Option>
              ))}
          </Select>
        </div>
      ) : null}
    </div>
  );
};

export default CheckSelect;