import { Input, DatePicker } from 'antd';
import moment from 'moment';

interface InputRuleProps {
  ruleConfig: any;
  onChange: (val: any) => void;
}

const InputRule = (props: InputRuleProps) => {
  const { ruleConfig, onChange } = props;

  const { valueTemplate, ruleName } = ruleConfig;

  const value = ruleConfig.ruleValue || ''; // 给上默认值空字符串

  const renderList: string[] = valueTemplate.split('').reduce((pre: string[], cur: string) => {
    // 第一个字符直接返回
    if (pre.length === 0) {
      return [cur];
    } else {
      // 这里pre的长度都是大于1的,取出最后一个
      const [lastItem, ...other] = pre.slice().reverse();
      // 如果只有一位，并且不是%，则拼接上然后返回
      if (lastItem.length === 1 && cur !== '%') {
        return [`${lastItem}${cur}`, ...other].reverse();
      } else if (cur === '%' || lastItem === '%s' || lastItem === '%t') {
        //   console.log('cur2', cur);
        //   console.log('lastItem2', lastItem);
        // 如果当前的是%,则返回
        return [...pre, cur];
      } else {
        // console.log('cur3', cur);
        // console.log('lastItem3', lastItem);
        return [`${lastItem}${cur}`, ...other].reverse();
      }
    }
  }, []);

  // 多个输入框,情况下分割
  let count = 0
  let valueCopyList:any = null
  let valueList:any = []
  if(renderList.includes('%t')) {
    renderList.forEach(ele => {
      if (ele === '%s' || ele === '%t') {
        count++
      }
    })

  } else {
    renderList.forEach(ele => {
      if (ele === '%s') {
        count++
      }
    })
  }

  
  
  if (count > 1) {
    valueCopyList = value.split(',');
    valueList = renderList.map((e) => {
      if (e === '%s' || e === '%t') {
        if (!value) {
          return {
            format: e,
            isValue: true,
            value: '',
          };
        }
        return {
          format: e,
          isValue: true,
          value: valueCopyList.shift(),
        };
      }
      return {
        format: e,
        isValue: false,
      };
    });
  } else if (count === 1) {
    valueList = renderList.map((e) => {
      if (e === '%s') {
        if (!value) {
          return {
            format: e,
            isValue: true,
            value: '',
          };
        }
        return {
          format: e,
          isValue: true,
          value: value,
        };
      }
      return {
        format: e,
        isValue: false,
      };
    })
  }


  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      {valueList.map((e, idx) => {
        if (e.format === '%s') {
          return (
            <Input
              disabled={ruleName === 'noTravelDate' || ruleName === 'validDate'}
              key={`${e.format}-${idx}`}
              style={{ width: 100, marginLeft: 8, marginRight: 8 }}
              value={valueList[idx].value}
              onChange={(e) => {
                valueList[idx].value = e.target.value;

                const text = valueList
                  .filter((e) => e.isValue)
                  .map((e) => e.value)
                  .join(',');
                // console.log('text', text);
                onChange(text);
              }}
            />
          );
        }
        if (e.format === '%t') {
          return (
            <DatePicker
              key={`${e.format}-${idx}`}
              // 后端老代码，时间只存6位，导致展示出问题，最好以后让后端把6位时间数据改成8位的
              value={!valueList[idx].value ? null : moment(`20${valueList[idx].value}`)}
              onChange={(e) => {
                valueList[idx].value = moment(e).format('YYMMDD');

                const text = valueList
                  .filter((e) => e.isValue)
                  .map((e) => e.value)
                  .join(',');
                // console.log('text', text);
                onChange(text);
              }}
              format="YYYY-MM-DD"
            />
          );
        }
        return <span key={`${e}-${idx}`}>{e.format}</span>;
      })}
    </div>
  );
};

export default InputRule;
