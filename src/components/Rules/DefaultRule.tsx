import { getComponentByType } from './index';

const DefaultRule = (props: {
  rule: {
    ruleDisplayName: string;
    id: number;
    ruleDom: string;
    propType: string;
    subRules: any[];
    valueTemplate: string;
    ruleValue: string;
    reactKey: string;
  };
  onChange: any;
}) => {
  const { rule, onChange } = props;

  let children = [];
  if ((rule?.subRules || []).length > 0) {
    children = rule.subRules.reduce((pre, cur, idx) => {
      return [
        ...pre,
        <div>
          <DefaultRule
            key={cur.id}
            rule={cur}
            onChange={(subRule: any) => {
              rule.subRules[idx] = subRule;
              onChange(JSON.parse(JSON.stringify(rule)));
            }}
          />
        </div>,
      ];
    }, []);
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      {rule.ruleDom
        ? getComponentByType({
            ruleConfig: rule,
            onChange: (val: any) => {
              onChange({
                ...rule,
                ruleValue: val,
              });
            },
          })
        : children}
    </div>
  );
};

export default DefaultRule;
