import { useState, useEffect } from 'react';
import { Select } from 'antd';

import api from '@/api';

const { Option } = Select;

interface TagInputProps {
  ruleConfig: any;
  onChange: (val: any) => void;
}

const TagInput = (props: TagInputProps) => {
  const { ruleConfig, onChange } = props;
  const [avaliableTags, setAvaliableTags] = useState<any[]>([]);
  const [searchText, setSearchText] = useState('');
  console.log('ruleConfig.ruleValue----', ruleConfig.ruleValue);
  useEffect(() => {
    if (ruleConfig.ruleValue) {
      setAvaliableTags(
        ruleConfig.ruleValue.split(',').map((ele: any) => {
          return {
            label: ele.split('#')[1],
            value: `${ele.split('#')[0]}#${ele.split('#')[1]}`,
            key: ele.split('#')[0],
          };
        }),
      );
    }
  }, []);
  // 把字符串转成数组
  // 规则商圈选品回显的值要处理
  let value: any;
  if (ruleConfig.ruleValue) {
    value = ruleConfig.ruleValue.split(',');
  }

  useEffect(() => {
    if (ruleConfig.ruleName && searchText) {
      api.common
        .getCommonSuggest({
          query: searchText,
          key: ruleConfig?.menuName?.split('#')[1] || ruleConfig.ruleName, // key有好几种
        })
        .then((res) => {
          if (res.code === '200' && res.msg === '操作成功') {
            setAvaliableTags(
              res.data.map((e) => ({
                label: e.topicName,
                value: `${e.topicId}#${e.topicName}`,
                key: e.topicId,
              })),
            );
          } else {
            setAvaliableTags([]);
          }
        });
    }
  }, [searchText, ruleConfig.ruleName]);

  // 传到外部的时候，需要将tags数组转化成字符串
  const onTagInputValueChange = (tags: any[], item: any) => {
    onChange(tags.join(','));
  };

  const searchChange = (e) => {
    setSearchText(e);
  };

  return (
    <Select
      style={{ width: 200 }}
      dropdownStyle={{ width: 200 }}
      // searchValue={searchText}
      optionFilterProp="label"
      onSearch={searchChange}
      mode="multiple"
      options={avaliableTags}
      filterOption={false}
      getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
      onChange={(e, item) => {
        // 把数组拼接起来传给后端
        onTagInputValueChange(e, item);
      }}
      // onBlur={() => {
      //   // 过滤掉搜索内容
      //   const temp = value.filter((e: string) => e !== searchText);
      //   onTagInputValueChange(temp);
      //   setSearchText('');
      // }}
      value={value}
    />
  );
};

export default TagInput;
