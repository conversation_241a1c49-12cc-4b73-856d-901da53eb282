import { useState, useEffect } from 'react';
import { Select, Input } from 'antd';
import { get } from 'lodash';

import Tree from './treeSelect'

const { Option } = Select;

import api from '@/api';

const Periphery = (props: any) => {

  const [treeData, setTreeData] = useState([])

  const { onChange, rule } = props;

  useEffect(() => {
    let data:any = {}
    if (rule.menuName) {
      data = {
        id: rule.menuName,
      };
    } 
    
    api.ruleType.getTreeData(data).then((res: any) => {
      if (res && res.data) {
        setTreeData(res.data);
      }
    });
  }, []);

  let value:any = {};
  if (rule.ruleValue) {
    value.firstVal = rule?.ruleValue?.split('|')[0]
    value.secondVal = rule?.ruleValue?.split('|')[1]
    value.thirdVal = rule?.ruleValue?.split('|')[2]
  } else {
    value = {}
  }

  const onChangeOneSelect = (value:any) => {
    triggerChange({firstVal: value})
  }

  const onChangeTwoSelect = (e:any) => {
    triggerChange({secondVal: e.target.value})
  }

  const onChangeThreeInput = (e:any) => {
    triggerChange({thirdVal: e.target.value})
  }

  const triggerChange = (changeValue:any) => {
    let temp = {...value, ...changeValue}
    onChange([temp.firstVal,temp.secondVal,temp.thirdVal].join('|'))
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Tree onChange={onChangeOneSelect} value={value?.firstVal ? value?.firstVal?.split(',') : []} treeData ={treeData}/>
      <span style={{ margin: '0 10px' }}>距离</span>
      <Input style={{ width: 60 }} value={value.secondVal} onChange={onChangeTwoSelect}/>
      <span>~</span>
      <Input style={{ width: 60 }} value={value.thirdVal} onChange={onChangeThreeInput}/>
      <span style={{ margin: '0 10px' }}>公里</span>
    </div>
  );
};

export default Periphery;