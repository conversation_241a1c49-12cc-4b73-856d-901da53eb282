import { useState, useEffect } from 'react';
import { TreeSelect } from 'antd';

import api from '@/api';

const Tree = (props: { value: any; onChange: any; treeData: any }) => {
  const [searchValue, setSearchValue] = useState('')
  let { value, onChange, treeData } = props;

  if (!value) {
    value = undefined;
  }

  const getValueFromChildren: (children: any[]) => any = (children: any[]) => {
    const temp = children
      .map((e) => {
        if (!e.children || e.children.length === 0) {
          return e.id;
        }
        return getValueFromChildren(e.children);
      })
      .join(',');
    return `p-${temp}`;
  };

  const formateTreeData = (node: any) => {
    if (node.children && node.children.length > 0) {
      return {
        title: node.content || node.name,
        value: getValueFromChildren(node.children),
        leaf: node.leaf || false,
        children: node.children.map(formateTreeData),
        layer: node.layer,
      };
    }
    return {
      title: node.content || node.name,
      value: `${node.id}`,
      sumbitValue: node.id,
      leaf: node.leaf || true,
      layer: node.layer,
    };
  };

  let treeDataAll = [];
  if (treeData) {
    treeDataAll = treeData.map(formateTreeData);
  }

  const onSearch = (value:any) => {
    setSearchValue(value)
  }

  const onBlur = () => {
    setSearchValue('')
  }

  return (
    <TreeSelect
      value={value}
      showCheckedStrategy={TreeSelect.SHOW_PARENT}
      getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
      treeData={treeDataAll}
      treeNodeFilterProp="title"
      onChange={(value: string[], labelList) => {
        const temp = value.map((e) => {
          // 去掉所有的p-
          return e?.toString().replaceAll('p-', '');
        });
        onChange(temp.join(','));
      }}
      treeCheckable
      placeholder="请选择"
      style={{ width: 200 }}
      searchValue={searchValue}
      onSearch={onSearch}
      onBlur={onBlur}
    />
  );
};

export default Tree;
