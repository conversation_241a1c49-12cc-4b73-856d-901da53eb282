import React, { useEffect, useState  } from 'react';
import { Select, DatePicker, Row, Col, InputNumber } from 'antd';
import moment from 'moment';

// 出行时间范围
const { RangePicker } = DatePicker;
const { Option } = Select;
const TravelTimeRange = (props: any) => {
  const { onChange, rule } = props;
  const [timeType, setTimeType] = useState('1');

  useEffect(() => {
    if (String(rule.ruleValue)?.indexOf(',') > -1) {
      setTimeType('1');
    } else {
      setTimeType('2');
    }
  }, [rule.ruleValue])

  const onValueChange = (val: string) => {
    setTimeType(val);
    if (val === '1') {
      onChange(',')
    } else {
      onChange('')
    }
  }

  const fixedTime = (timeType === '1' && rule.ruleValue) ? String(rule.ruleValue)?.split(',') || [] : [];

  const disabledDate = (current: any) => {
    if (current) {
      if ((current < moment().subtract(1, 'day')) || (current > moment().add(90, 'day'))) {
        return true;
      }
      return false;
    }
    return false;
  };


  return (
    <Row>
      <Col style={{ marginRight: 4 }}>
        <Select value={timeType} onChange={onValueChange}>
          <Option value={'1'}>固定时间</Option>
          <Option value={'2'}>相对时间</Option>
        </Select>
      </Col>
      {
        timeType === '1' && (
          <Col>
            <RangePicker
              value={
                [
                  fixedTime[0] ? moment(fixedTime[0]) : null,
                  fixedTime[1] ? moment(fixedTime[1]) : null
                ]
              }
              onChange={(date, dateString) => {
                if (dateString.length){
                  onChange(dateString.join(','));
                } else {
                  onChange(',')
                }
              }}
              disabledDate={disabledDate}
            />
          </Col>
        )
      }
      {
        timeType === '2' && (
          <Col>
            <Row>
              <Col>
                <InputNumber
                  value={rule.ruleValue}
                  onChange={(val) => {onChange(val)}}
                  max={90}
                  min={0}
                />
              </Col>
              <Col style={{fontSize: 12}}>
                相对天数N(正数:今天开始至未来第N天;负数:过去第N天开始至今天)
              </Col>
            </Row>
          </Col>
        )
      }
    </Row>
  )
};

export default TravelTimeRange;
