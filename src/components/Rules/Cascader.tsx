import { useState, useEffect } from 'react';
import { TreeSelect } from 'antd';

import api from '@/api';

/**
 * 规则类型——地区选择
 */
const AREASELECT = 'areaSelect';

const Cascader = (props: { rule: any; onChange: any }) => {
  const { rule, onChange } = props;

  const [treeData, setTreeData] = useState<any>();

  // console.log('rule', rule);

  let value;
  if (treeData) {
    if (rule.ruleName === AREASELECT) {
      if (rule.ruleValue) {
        value = rule.ruleValue.split(',').map((ele: any) => {
          return ele.replaceAll('city#', '');
        });
      } else {
        value = [];
      }
    } else {
      value = rule.ruleValue ? rule.ruleValue.split(',').map((e) => e) : [];
    }
  }

  const addLayer = (node: any) => {
    const setLayer = (node: any, layer: number) => {
      node.forEach((element: any) => {
        element.layer = layer;
        if (element.children) {
          setLayer(element.children, layer + 1);
        }
      });
    };

    setLayer(node, 1);

    return node;
  };

  const getValueFromChildren: (children: any[]) => any = (children: any[]) => {
    const temp = children
      .map((e) => {
        if (!e.children || e.children.length === 0) {
          return e.id;
        }
        return getValueFromChildren(e.children);
      })
      .join(',');
    return `p-${temp}`;
  };

  const formateTreeData = (node: any) => {
    if (node.children && node.children.length > 0) {
      return {
        title: node.content,
        value: getValueFromChildren(node.children),
        leaf: node.leaf || false,
        children: node.children.map(formateTreeData),
        layer: node.layer,
      };
    }
    return {
      title: node.content,
      value: `${node.id}`,
      sumbitValue: node.id,
      leaf: node.leaf || true,
      layer: node.layer,
    };
  };

  /**
   * 给父级value增加p-，用于区分选中的key
   * @param layer 层数
   * @returns
   */
  const layp = (layer: number) => {
    let temp = '';
    for (var i = 0; i < layer; i++) {
      temp += 'p-';
    }
    return temp;
  };

  useEffect(() => {
    let data:any = {}
    if (rule.menuName) {
      data = {
        id: rule.menuName,
      };
    } else {
      data = {
        id: rule.ruleName,
      };
      if (rule.ruleName === 'poiTitlePlayTag') {
        data.id = 'poiTitlePlayTag';
      }
      if (rule.ruleName === 'playTagRelScore') {
        data.id = 'playTagId';
      }
      if (rule.ruleName === 'sellerMainCateScoreParent') {
        data.id = 'sellerMainCate';
      }
      if (rule.ruleName === 'poiTagRelScore') {
        data.id = 'poiTagIdTree';
      }
  
      if (rule.ruleName === 'jiujingPackagePOI') {
        data.id = 'poiTitlePlayTag';
      }
  
      // 新增poi选品规则树
      if (rule.ruleName === 'poiCategorySelect') {
        data.id = 'poiCategorySelect'
      }
    }
    
    api.ruleType.getTreeData(data).then((res: any) => {
      // console.log('res', res.data);
      const temp = addLayer(res.data).map(formateTreeData);
      setTreeData(temp);
    });
  }, []);

  return (
    <TreeSelect
      value={value}
      showCheckedStrategy={TreeSelect.SHOW_PARENT}
      treeData={treeData}
      treeNodeFilterProp="title"
      getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
      onChange={(value: string[]) => {
        const temp = value.map((e) => {
          // 去掉所有的p-
          return e?.toString().replaceAll('p-', '');
        });
        if (rule.ruleName === AREASELECT) {
          let newTemp = temp
            .join(',')
            .split(',')
            .map((ele) => {
              return `city#${ele}`;
            });
          onChange(newTemp.join(','));
        } else {
          onChange(temp.join(','));
        }
      }}
      treeCheckable
      placeholder="请选择"
      style={{ width: 300 }}
    />
  );
};

export default Cascader;
