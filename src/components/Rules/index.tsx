import React from 'react';
import { Checkbox, Radio, Select } from 'antd';

import InputRule from './InputRule';
import TagInput from './TagInput';
import CheckSelect from './CheckSelect';
import TreeRule from './TreeRule';
import Cascader from './Cascader';
import DayNext from './DayNext';
import Serious from './Serious'
import MultiSelect from './MultiSelect'
import Periphery from './Periphery';
import RouteRule from './RouteRule';
import TravelTimeRange from './TravelTimeRange';

const CheckboxGroup = Checkbox.Group;
const RadioGroup = Radio.Group;
const Option = Select.Option;

export const getComponentByType = (props: {
  ruleConfig: {
    ruleValue: string;
    ruleDom: string;
    valueTemplate: string;
    reactKey: string;
  };
  onChange: any;
}) => {
  //  onChange入参统一是value
  const { ruleConfig, onChange } = props;
  switch (ruleConfig?.ruleDom) {
    case 'INPUT':
      return <InputRule ruleConfig={ruleConfig} onChange={onChange} />;
    case 'TAG_INPUT':
      return <TagInput ruleConfig={ruleConfig} onChange={onChange} />;
    case 'CHECKBOX':
      const checkBoxOptions: any = [];
      const checkBoxInfoList = ruleConfig.valueTemplate.replace(/%s/g, '').split(' ');
      // console.log(checkBoxInfoList);
      checkBoxInfoList.forEach((checkboxInfo: any) => {
        const [v, text] = checkboxInfo.split(',');
        checkBoxOptions.push({
          label: text || checkboxInfo,
          value: v,
        });
      });
      return (
        <CheckboxGroup
          options={checkBoxOptions}
          value={ruleConfig.ruleValue ? ruleConfig.ruleValue.split(',') : []}
          onChange={(vals) => {
            onChange(vals.join(','));
          }}
        />
      );
    case 'RADIO':
      const radioOptions: any[] = [];
      const radioInfoList = ruleConfig.valueTemplate.replace(/%s/g, '').split(' ');
      radioInfoList.forEach((checkboxInfo: any) => {
        const [v, text] = checkboxInfo.split(',');
        radioOptions.push({
          label: text || checkboxInfo,
          value: v,
        });
      });
      return (
        <RadioGroup
          options={radioOptions}
          onChange={(e) => {
            onChange(e.target.value);
          }}
          value={ruleConfig.ruleValue}
          // defaultValue={record.ruleValueList[domIndex]}
        />
      );
    case 'RADIO_SHEET':
      return <Checkbox disabled={true} defaultChecked={true} />;
    case 'SELECTOR':
      const selectInfoList = ruleConfig.valueTemplate.split('%s');
      return (
        <Select style={{ width: 150 }} value={ruleConfig.ruleValue} onChange={(e) => onChange(e)}>
          {selectInfoList && selectInfoList.map((item) => <Option value={item}>{item}</Option>)}
        </Select>
      );
    case 'CASCADER':
      return <Cascader rule={ruleConfig} onChange={onChange} />;
    case 'TREE':
      return <TreeRule key={ruleConfig.reactKey} rule={ruleConfig} onChange={onChange} />;
    case 'doubleSelect': {
      return <CheckSelect onChange={onChange} record={ruleConfig} />;
    }
    case 'linePlayLabel':
      return <CheckSelect onChange={onChange} record={ruleConfig} />;
    case 'TVSBC_I':
      return <DayNext onChange={onChange} rule={ruleConfig}/>;
    case 'TVSBC_S':
      return <Serious onChange={onChange} rule={ruleConfig}/>;
    case 'MULTISELECT':
      return <MultiSelect onChange={onChange} rule={ruleConfig}/>
    case 'PERIPHERY':
      return <Periphery onChange={onChange} rule={ruleConfig}/>
    case 'TEXT':
      return <RouteRule onChange={onChange} rule={ruleConfig} />
    case 'DATE':
      return <TravelTimeRange onChange={onChange} rule={ruleConfig} />
    default:
      return <div></div>;
  }
};
