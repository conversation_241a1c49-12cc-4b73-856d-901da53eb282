import React from 'react';
import { Input } from 'antd';

// 航线
const { TextArea } = Input;
const RouteRule = (props: any) => {
  const { onChange, rule } = props;

  const onValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const list = e.target.value?.split('\n') || [];
    const cityStr = (list || []).join(',');
    onChange(cityStr);
  };

  const testValue = (val: string) => {
    let flag = false;
    if (!val) return;
    const result = val.split(',') || [];
    if(result.includes('')) {
      flag = true;
    };
    result.forEach((item:string) => {
      const list = item.split(' ') || [];
      if (list.length > 2) {
        flag = true;
      }
    })
    return flag;
  }

  const handleValue = (val: string) => {
    const cityValue = val?.split(',') || [];
    return (cityValue || []).join('\n')
  };

  return (
    <div>
      <TextArea
        value={handleValue(rule.ruleValue)}
        onChange={onValueChange}
        status={testValue(rule.ruleValue) ? 'error' : ''}
      />
      {
        testValue(rule.ruleValue) ? (
          <span style={{fontSize: 12, color: 'red' }}>格式错误,请检查是否有多余的空格或空行</span>
        ) : null
      }
    </div>
  )
}

export default RouteRule;
