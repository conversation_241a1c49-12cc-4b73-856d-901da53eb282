import { useState, useEffect } from 'react';
import { Select, InputNumber } from 'antd';

const { Option } = Select;

import api from '@/api';

const DayNext = (props: any) => {

  const { onChange, rule } = props;

  let value:any = {};
  if (rule.ruleValue) {
    value.firstVal = rule?.ruleValue?.split(',')[0]
    value.secondVal = rule?.ruleValue?.split(',')[1]
  } else {
    value = {}
  }

  const onChangeSelect = (value:any) => {
    triggerChange({firstVal: value})
  }

  const onChangeNumber = (value:number) => {
    triggerChange({secondVal: value})
  }

  const triggerChange = (changeValue:any) => {
    let temp = {...value, ...changeValue}
    onChange([temp.firstVal,temp.secondVal].join(','))
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Select style={{ width: 120 }} value={value.firstVal} onChange={onChangeSelect}>
        <Option value=">">大于</Option>
        <Option value=">=">大于等于</Option>
        <Option value="<">小于</Option>
        <Option value="<=">小于等于</Option>
        <Option value="=">等于</Option>
      </Select>
      <InputNumber onChange={onChangeNumber} value={value.secondVal}/>
    </div>
  );
};

export default DayNext;