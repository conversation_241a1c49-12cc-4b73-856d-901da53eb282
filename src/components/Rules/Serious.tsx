import { useState, useEffect } from 'react';
import { Select, InputNumber } from 'antd';

const { Option } = Select;

interface dataType {
  content?: string;
  id: string;
}

import api from '@/api';

const Serious = (props: any) => {
  
  const [dataList, setDataList] = useState([])

  const { onChange, rule } = props;

  useEffect(() => {
    let data:any = {}
    if (rule.menuName) {
      data = {
        id: rule.menuName,
      }
    }
    
    api.ruleType.getTreeData(data).then((res: any) => {
      setDataList(res?.data)
    });
  }, []);

  let value:any = {};
  if (rule.ruleValue) {
    value.firstVal = rule?.ruleValue?.split(',')[0]
    value.secondVal = rule?.ruleValue?.split(',')[1]
  } else {
    value = {}
  }

  const onChangeSelectOne = (value:any) => {
    triggerChange({firstVal: value})
  }

  const onChangeSelectTwo = (value:number) => {
    triggerChange({secondVal: value})
  }

  const triggerChange = (changeValue:any) => {
    let temp = {...value, ...changeValue}
    onChange([temp.firstVal,temp.secondVal].join(','))
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Select style={{ width: 120 }} value={value.firstVal} onChange={onChangeSelectOne}>
        <Option value="=">等于</Option>
        <Option value="!=">不等于</Option>
      </Select>
      <Select style={{ width: 220 }} value={value.secondVal} onChange={onChangeSelectTwo}>
        {dataList && dataList.length > 0 && dataList.map((ele:dataType) => (
          <Option value={ele.id}>{ele.content}</Option>
        ))}
      </Select>
    </div>
  );
};

export default Serious;