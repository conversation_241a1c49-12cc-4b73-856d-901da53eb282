import React, { useState, useEffect, useRef } from 'react';
import { Select } from 'antd';
import { useGlobal } from '@ali/use-global';
import { history } from 'umi';

import api from '@/api';

const { Option } = Select;

type OptionProps = {
  label: string;
  value: number;
  dataSource: string;
  activityOwner: string;
};

const TOPCollectSearch = ({ value }: { value?: any }) => {
  const [searchText, setSearchText] = useState('');
  const [dataSource, setDataSource] = useState<OptionProps[]>([]);
  const [global, setGlobal] = useGlobal();
  const timer = useRef(0);

  useEffect(() => {
    clearTimeout(timer.current);
    timer.current = setTimeout(() => {
      api.activity
        .topSearchList({
          query: searchText,
        })
        .then((res) => {
          let temp = res.data.map((e: Activity) => {
            return {
              ...e, // 其他信息还是保留，其他步骤要用
              label: e.activityTitle,
              value: e.id,
            };
          });
          // setDataSource(temp.filter((e) => e.parentId !== 0)); // 0表示是主活动，暂时先过滤掉
          setDataSource(temp)
        });
    }, 200) as unknown as number;
  }, [searchText]);

  useEffect(() => {
    if (!searchText) {
      setGlobal({
        query:null
      })
    }
  },[searchText]);

  useEffect(()=>{
    return () => {
      setGlobal({
      query:null
    })}
  },[])

  const onChange = (e: any, item: any) => {
    // https://pre-tripgalaxy.alibaba-inc.com/#/manageCollect/manageChild?activityTitle=zll%E6%B5%8B%E8%AF%95travel&id=58846&isChild=1&isOwner=true
    // https://pre-tripgalaxy.alibaba-inc.com/#/createCollect?type=1&step=2&collectionId=59460
    // 如果是品集，直接进入选品页面
    if (item && item.option) {
      // 老逻辑
      // if (item.option.parentId) {
      //   // 有这个字段说明是品集。，而不是活动
      //   window.location.href = `https://${window.location.host}/#createCollect?type=${
      //     item.option.createType + 1
      //   }&step=2&collectionId=${e}`;
      // } else {
      //   // 这部分暂时没用上，因为现在下拉的搜索不是全部的
      //   window.location.href = `https://${window.location.host}/#/manageCollect/manageChild?activityTitle=${
      //     item.option.activityTitle
      //   }&id=${item.option.parentId}&isChild=${item.option.parentId ? 1 : 0}&isOwner=${item.option.isOwner}`;
      // }
      if (item.option.parentId) {
        setGlobal({
          query:item.option.id,
          pageNos: 1
        })
        // 有这个字段说明是子活动
        window.location.href = `https://${window.location.host}/#/manageCollect/manageChild?activityTitle=${
          item.option.activityTitle
        }&id=${item.option.parentId}&isChild=${item.option.parentId ? 1 : 0}&isOwner=${item.option.isOwner}&isSearch=${true}`;
      } else {
        setGlobal({
          query:item.option.id,
          pageNos: 1
        })
        // 如果isOwner为true跳转我创建的，否则跳转到全部品集
        // 没有这个字段或则这个字段为0就是主活动
        if (item.option.isOwner) {
          window.location.href = `https://${window.location.host}/#/manageCollect`;
        } else {
          window.location.href = `https://${window.location.host}/#/manageCollect?isOther=${true}`;
        }

      }
    } else {
      setGlobal({
        query:null,
        pageNos: 1
      })
    }
  };

  const onClear = () => {
    setSearchText('')
    window.location.href = `https://${window.location.host}/#/manageCollect?isOther=${true}`;
  }

  return (
    <Select
      style={{ width: 300 }}
      placeholder="全局搜索选品集/选品池"
      onSearch={(val) => {
        setSearchText(val);
      }}
      onClear={onClear}
      allowClear={true}
      value={value}
      onChange={onChange}
      showSearch
      filterOption={false}
    >
      {dataSource.map((e) => (
        // option用来存放原始数据
        <Option key={e.value} value={e.value} option={e}>
          {e.dataSource === 'GALAXY' ? `[集]-${e.label} ${e.dataSource} ${e.activityOwner}` :`[池]-${e.label} ${e.dataSource} ${e.activityOwner}`}
        </Option>
      ))}
    </Select>
  );
};

export default TOPCollectSearch;
