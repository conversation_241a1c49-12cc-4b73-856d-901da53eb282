import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Card, Table, Modal, Popover, Button } from 'antd';
import RuleShowBox from '@/components/RuleShowBox'
import api from '@/api';

interface ModalRecordProps {
  isRecordVisible: boolean;
  closeRecord: () => void;
  ids?: number|string
  isRule?: boolean;
}

const ModalRecord = (props: ModalRecordProps) => {
  const [state, setState] = useSet({
    pageSize: 10,
    pageNo: 1,
    total: 0,
    loading: false,
    dataSource: [],
  });
  const { isRecordVisible, closeRecord, ids, isRule } = props;
  const { loading, pageSize, pageNo, total, dataSource } = state;


  useEffect(()=> {
    let data:any = {}
    if (isRule) {
      data = {
        id: ids,
        pageNo,
        pageSize,
        operation:'SaveActivityRule'
      }
    } else {
      data = {
        id: ids,
        pageNo,
        pageSize,
      }
    }
    setState({
      loading: true,
    });
    api.activity.GetActivityOpRecord(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      }
    });
  },[])

  const handleCancel = () => {
    closeRecord();
  };

  const content = (record: any)=> {
    return <div className="rules-maker">
      <RuleShowBox rules={JSON.parse(record.data)}/>
    </div>
    
  }

  const columns = [{
    title: '时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'nickName',
    key: 'nickName',
    align: 'center',
    render: (text: any, record: any, index: number) => (
      record.empId ? record.empId : '暂无'
    ),
  },{
    title: '操作内容',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
  },{
    title: '备注',
    dataIndex: 'operationEn',
    key: 'operationEn',
    align: 'center',
    width: 400,
    render: (text: any, record: any, index: number) => {
      switch (record.operationEn) {
        case 'SaveActivityRule':
          return <Popover content={content(record)} trigger="hover">
          <Button>显示规则</Button>
        </Popover>
        case 'SaveBlack':
          return <div style={{wordBreak:'break-all'}}>{record?.data ? record?.data : '暂无'}</div>
        case 'RemoveBlack':
          return <div style={{wordBreak:'break-all'}}>{record?.data ? record?.data : '暂无'}</div>
        default:
          return <div style={{wordBreak:'break-all'}}>{record?.data ? record?.data : '暂无'}</div>
      }
      
    },
  }];

  // 切换页码
  const onPaginationChange = (pageNum: number, pageSize: any) => {
    let data:any = {}
    if (isRule) {
      data = {
        id: ids,
        pageNo: pageNum,
        pageSize,
        operation:'SaveActivityRule'
      }
    } else {
      data = {
        id: ids,
        pageNo: pageNum,
        pageSize,
      }
    }
    setState({
      loading: true,
    });
    api.activity.GetActivityOpRecord(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      }
    });
  };

  return (
    <Modal title="历史规则记录" visible={isRecordVisible} onCancel={handleCancel} width="80vw" footer={null}>
      <Table
        // scroll={{ x: 1500 }}
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        rowKey={(record) => record.id}
        pagination={{
          pageSize: pageSize,
          current: pageNo,
          total: total,
          onChange: (num, pageSize) => onPaginationChange(num, pageSize),
        }}
      />
    </Modal>
  );
};

export default ModalRecord;
