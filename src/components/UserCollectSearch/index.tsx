import React, { useState } from 'react';
import { Select, Spin, Avatar } from 'antd';
import debounce from 'lodash/debounce';
import { get } from 'lodash';

interface IUsersRow {
  empId: string;
  nick: string;
  name: string;
  pic: string;
}
interface ISearchUserProps {
  value?: Array<string> | Object;
  onChange: Function;
  name?: string;
  mode?: any;
  options?: any;
  style: Object;
  disabled?: boolean;
}

const UserCollectSearch = (props: ISearchUserProps) => {
  let { value = undefined, onChange, name, mode = '', options = {}, style = {}, disabled } = props;
  mode = mode || options.mode || ''; // 兼容 FormRender，一般组件都是props传递下来，FR只能通过schema的ui:options传递下来
  if (mode === 'multiple' && !value) {
    value = [];
  } else if (!value) {
    value = undefined;
  }

  const [data, setData] = useState<IUsersRow[]>([]);
  const [fetching, setFetching] = useState<boolean>(false);
  let lastFetchId: number = 0;

  const fetchUser = debounce((value: string) => {
    lastFetchId += 1;
    const fetchId = lastFetchId;
    setData([]);
    setFetching(true);
    (window.location.href.includes('fliggy.alibaba-inc.com')
      ? fetch(`/common/GetUserInfo.do?key=${value}&mock=a`)
      : fetch(`/common/GetUserInfo.do?key=${value}&mock=a`)
    )
      .then((response: any) => response.json())
      .then((res: any) => {
        if (fetchId !== lastFetchId) {
          // for fetch callback order
          return;
        }
        let data = get(res, 'data', []);
        setData(data);
        setFetching(false);
      });
  }, 800);

  const handleChange = (value: any) => {
    setFetching(false);
    // 为了兼容 FormRender，所以需要有下面的 name 判断，一般的组件封装直接通过回调函数把value带回去就行了，这里的onChange(name, value)主要是为了保证FR知道自定义组件的key
    typeof onChange === 'function' && name ? onChange(name, value) : onChange(value);
  };

  return (
    <Select
      allowClear
      mode={mode}
      disabled={disabled}
      showSearch
      getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
      // @ts-ignore
      value={value}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      filterOption={false}
      onSearch={fetchUser}
      onChange={handleChange}
      // style={{ ...style, width: '100%' }}
      style={{ width: '100%', ...style }}
      placeholder="请输入花名进行查询"
      optionLabelProp="label"
    >
      {data.map((d: any) => (
        <Select.Option value={(d.nick && d.empId) ? `${d.nick}[${d.empId}]` : d.nick} key={d.empId}>
          <Avatar src={d.pic} />
          <span style={{ marginLeft: '8px' }}>{`${d.nick}(${d.name})`}</span>
        </Select.Option>
      ))}
    </Select>
  );
};

export default UserCollectSearch;
