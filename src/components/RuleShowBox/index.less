.rules-maker {
  .row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .row-title {
      min-width: 100px;
      text-align: right;
    }
  }
  .first-row {
    align-items: flex-start;
    .row-content {
      flex-grow: 1;
    }
  }
  .free-select {
    padding: 20px;
    border: 1px solid #d9d9d9;
  }
  .upload {
    padding: 20px;
    border: 1px solid #d9d9d9;
  }
  .inventory {
    padding: 20px;
    border: 1px solid #d9d9d9;
  }
  .rules-box {
    position: relative;
    display: flex;
    margin-bottom: 10px;
    .rules-container {
      flex-grow: 1;
      div:last-of-type {
        margin-bottom: 0;
      }
      .single-rule-group {
        min-width: 500px;
        margin-bottom: 10px;
        padding: 10px;
        background-color: #f9fafc;
        border: 1px solid #d9d9d9;

        .rule-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          padding: 4px;
          border: 1px solid lightgray;
        }
        .content {
          display: flex;
          align-items: stretch;
          margin-bottom: 10px;
          .items {
            display: flex;
            flex-direction: column;
            div:last-of-type {
              margin-bottom: 0;
            }
          }
          .and-icon {
            display: flex;
            flex-shrink: 0;
            align-items: center;
            .icon-line {
              width: 10px;
              min-height: 40px;
              margin-right: 10px;
              border: 1px solid #d9d9d9;
              border-left: none;
            }
          }
        }
      }
    }

    .or-icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      align-self: center;
      .icon-line {
        width: 10px;
        min-height: 140px;
        margin-right: 10px;
        border: 1px solid #d9d9d9;
        border-left: none;
      }
    }
  }
}