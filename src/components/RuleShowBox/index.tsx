import { useState, useEffect, useRef, useCallback } from 'react';
import { Space, Button } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { cloneDeep } from 'lodash';

import { getComponentByType } from '@/components/Rules/index';

import { getReactKey } from '@/utils/utils';
import DefaultRule from '@/components/Rules/DefaultRule';

import './index.less'

const RulesBox = (props: {
  rules: any;
}) => {
  const [height, setHeight] = useState(0); //计算或的高度
  const { rules } = props;

  const measuredRef = useCallback(
    (node) => {
      if (node !== null) {
        setHeight(node.getBoundingClientRect().height - 84);
      }
    },
    [rules],
  );

  // 清空无rom

  const emptyRuleValue = (subRules: any) => {
    subRules.forEach((el: any) => {
      if (el.subRules) {
        emptyRuleValue(el.subRules);
      } else {
        el.ruleValue = '';
      }
    });
  };

  const renderSingleRuleGroup = (ruleGroup: any, index: number) => {
    const { ruleConfigList, reactKey: ruleGroupReactKey } = ruleGroup;
    return (
      <div className="single-rule-group" key={ruleGroupReactKey}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div className="title">{`规则组${index + 1}`}</div>
          <div
            className="delete-icons"
            style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}
          >
          </div>
        </div>
        <div className="content">
          <div className="items">
            {ruleConfigList.map((ruleConfig: any, ruleIdx: number) => {
              return (
                <div className="rule-item" key={ruleConfig.reactKey}>
                  <Space>
                    <div style={{ paddingLeft: 50, textAlign: 'right' }}>{ruleConfig.ruleDisplayName}:</div>
                    {ruleConfig.ruleDom ? (
                      getComponentByType({
                        ruleConfig,
                        onChange: (value: string) => {},
                      })
                    ) : (
                      <DefaultRule
                        rule={ruleConfig}
                        onChange={(value: any) => {
                        }}
                      />
                    )}
                  </Space>
                </div>
              );
            })}
          </div>
          {ruleConfigList.length > 1 && (
            <div className="and-icon">
              <div className="icon-line" style={{ height: 'calc(100% - 40px)' }}></div>
              <span>且</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="rules-box">
      <div className="rules-container">
        <div ref={measuredRef} style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ width: '100%' }}>{rules.length > 0 && rules.map(renderSingleRuleGroup)}</div>
          {rules.length > 1 && (
            <div className="or-icon">
              <div className="icon-line" style={{ height }}></div>
              <span>或</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RulesBox;
