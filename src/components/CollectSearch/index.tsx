import { useState, useEffect, useRef } from 'react';
import { Select } from 'antd';

import api from '@/api';

const { Option } = Select;

const CollectSearch = ({
  value,
  onChange,
  hasAll,
  style,
}: {
  value?: any;
  onChange?: any;
  hasAll?: boolean;
  style?: React.CSSProperties;
}) => {
  const [searchText, setSearchText] = useState('');
  const [dataSource, setDataSource] = useState<{ label: string; value: number }[]>([]);
  const timer = useRef(0);

  useEffect(() => {
    api.activity
      .list({
        pageNo: 1,
        pageSize: 5000,
        isChild: 0,
        parentId: 0,
        isOwner: true, // 先去掉这个活动搜索的限制
        query: searchText,
      })
      .then((res) => {
        // console.log('res', res);
        let temp = res.data.map((e: Activity) => {
          return {
            ...e, // 其他信息还是保留，其他步骤要用
            label: e.activityTitle,
            value: e.id,
          };
        });
        // 如果需要全部，展示全部
        if (hasAll) {
          temp = [{ label: '全部', value: -1 }, ...temp];
        }
        setDataSource(temp);
      });
  }, [searchText]);

  return (
    <Select
      style={{ width: 300, ...(style || {}) }}
      onSearch={(val) => {
        setSearchText(val);
      }}
      value={value}
      onChange={onChange}
      showSearch
      // filterOption={(input, option) => {
      //   return (option?.option?.label as string).toLowerCase().indexOf(input.toLowerCase()) >= 0;
      // }}
      filterOption={false}
    >
      {dataSource.map((e) => (
        // option用来存放原始数据
        <Option key={e.value} value={e.value} option={e}>
          {e.label}
        </Option>
      ))}
    </Select>
  );
};

export default CollectSearch;
