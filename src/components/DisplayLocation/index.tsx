import React, { useState, useEffect } from 'react';
import { Cascader } from 'antd';
import _ from 'lodash';
import { QuestionCircleOutlined } from '@ant-design/icons';
import api from '@/api'
const DisplayLocation = (props: any) => {
  let { value = undefined, onChange, name, mode = '', options = {}, disabled, addons } = props;
  mode = mode || options.mode || ''; // 兼容 FormRender，一般组件都是props传递下来，FR只能通过schema的ui:options传递下来

  console.log('addons0000',props)

  if (mode === 'multiple' && !value) {
    value = [];
  } else if (!value) {
    value = undefined;
  }

  const [optionsData, setOptionsData] = useState<any[]>([]);
  
  useEffect(()=> {
    console.log('value00---',value)
    if (value) {
      const data = {
        dataType: value[0]
      }
      api.rule.getRuleLevelTree(data).then((res: any) => {
        if (res.code === '200') {
          setOptionsData(res.data.childNode)
          value = []
        } else {
          value = []
        }
      })
    }
  },[value])

  const handleChange = (value: any) => {
    console.log(value,'value')
    // 为了兼容 FormRender，所以需要有下面的 name 判断，一般的组件封装直接通过回调函数把value带回去就行了，这里的onChange(name, value)主要是为了保证FR知道自定义组件的key
    typeof onChange === 'function' && name ? onChange(name, value) : onChange(value);
  };

  return <Cascader style={{width:'100%'}} options={optionsData} onChange={handleChange} placeholder="请选择展示位置" fieldNames={{ label: 'value', value: 'id', children: 'childNode' }}/>;
};

export default DisplayLocation;
