import Avatar from './Avatar';
import { But<PERSON>, Alert } from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { useGlobal } from '@ali/use-global';
import TOPCollectSearch from '@/components/TOPCollectSearch';
import Marquee from 'react-fast-marquee';
import { useState, useEffect, useRef } from 'react';

import api from '@/api';
import styles from './index.less';

const Header = (props: { collapsed: boolean; onCollapsedChange: () => void }) => {
  const { collapsed, onCollapsedChange } = props;
  const [global, setGlobal] = useGlobal();
  const [queeText, setQueeText] = useState('');
  const [startTime, setStartTime] = useState<number>(0);
  const [endTime, setEndTime] = useState<number>(0);

  const hash = window.location.hash;

  const showSearch =
    hash.toLocaleLowerCase().includes('#/createcollect') ||
    hash.toLocaleLowerCase().includes('#/managecollect') ||
    hash.toLocaleLowerCase().includes('#/manageCollect/managechild');

  useEffect(() => {
    api.activity.notice().then((res: any) => {
      if (res.code === '200' && res.data && res.data.content && res.data.start && res.data.end) {
        setQueeText(decodeURIComponent(res.data.content));
        setStartTime(res.data.start);
        setEndTime(res.data.end);
      }
    });
  }, []);

  const isTime = () => {
    const nowTime = new Date().valueOf();
    if (nowTime >= startTime && nowTime <= endTime) {
      return true;
    }
    return false;
  };

  return (
    <div className={styles.header}>
      <div className={styles.header_left}>
        <div
          onClick={onCollapsedChange}
          style={{
            cursor: 'pointer',
            fontSize: '16px',
          }}
        >
          {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </div>
        <div className={styles.title}>星辰策略选品平台</div>
        {showSearch && <TOPCollectSearch value={global?.query} />}
        <div style={{ width: '50%', marginLeft: '20px' }}>
          {isTime() ? (
            <Alert
              banner
              showIcon={false}
              message={
                <Marquee pauseOnHover gradient={false}>
                  <div style={{ height: '100%' }} dangerouslySetInnerHTML={{ __html: queeText }}></div>
                </Marquee>
              }
            />
          ) : null}
        </div>
      </div>
      <div className="header-right">
        <Button target="_blank" type="link" href="https://yuque.antfin-inc.com/docs/share/17888c8e-6325-49b9-8437-ae8fa61b54a6?#">
          使用文档
        </Button>
        <Avatar />
      </div>
    </div>
  );
};

export default Header;
