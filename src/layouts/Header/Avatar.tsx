import { Avatar, Spin, Space } from 'antd';
import { useGlobal } from '@ali/use-global';

const UserAvatar = () => {
  const [store] = useGlobal();
  const { currentUser } = store;

  return currentUser && currentUser.empId ? (
    <Space style={{ marginRight: '12px' }}>
      <Avatar src={`https://work.alibaba-inc.com/photo/${currentUser.empId}.220x220.jpg`}/>
      <span>{currentUser.name}</span>
    </Space>
  ) : (
    <span style={{ marginRight: '12px' }}>
      <Spin size="small" />
    </span>
  );
};

export default UserAvatar;
