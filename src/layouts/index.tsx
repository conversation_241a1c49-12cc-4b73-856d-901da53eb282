import React, { useState, useEffect } from 'react';
import ProLayout, { PageContainer } from '@ant-design/pro-layout';
import { Link, history } from 'umi';
import Store, { useGlobal } from '@ali/use-global';
import Header from './Header';
import { get } from 'lodash';

import styles from './index.less'

import api from '@/api';

const menuDataRender = (menuList: any, isGalaxyAdmin: boolean = false) =>
  menuList.map((item: any) => {
    // 如果是管理员模块，只有星辰管理员才能看到
    if (item.path === '/admin' && !isGalaxyAdmin) {
      return null;
    }
    return {
      ...item,
      children: item.children ? menuDataRender(item.children, isGalaxyAdmin) : [],
    };
  }).filter(Boolean); // 过滤掉null值

const BasicLayout = ({ children, ...rest }: any) => {
  const [global, setGlobal] = useGlobal();

  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {

    // 老版本的地址跳转到新版本地址
    if (history.location.pathname === '/index') {
      history.push('/')
    }

    if (history.location.pathname === '/selection') {
      const ids = history.location?.query?.id
      history.push(`/createCollect/goodsPool?collectionId=${ids}`)
    }
    
    api.common.getUserInfo().then((res:any) => {
      if (res.code === '200' && res.msg === '操作成功') {
        setGlobal({
          currentUser: res.data,
        });
        fetch(`/common/GetUserInfo.do?key=${res.data.empId}&mock=a`)
        .then((response: any) => response.json())
        .then((res) => {
          if (res && res.code === '200') {
            setGlobal({
              createOwner: get(res,'data[0]',{})
            });
          }
        });
      }
    });

    // 检查是否为星辰管理员
    api.common.isGalaxyAdmin().then((res: any) => {
      if (res.code === '200') {
        setGlobal({
          isGalaxyAdmin: res.data === 'true' || res.data === true,
        });
      }
    });
  }, []);

  // 在iframe中隐藏掉头部和左侧菜单
  if(window.location.href.includes('isFrame=true')){
    return (
      <div
        style={{
          height: '100vh',
          background: '#f0f2f5'
        }}
      >
        {children}
      </div>
    )
  }

  return (
    <div
      style={{
        height: '100vh',
      }}
    >
      <ProLayout
        className = {styles.layout_main}
        title="星辰 Galaxy"
        menuHeaderRender={(logo: string, title: string) => {
          return <Link to="/">{title}</Link>;
        }}
        collapsed={collapsed}
        collapsedButtonRender={false}
        onCollapse={setCollapsed}
        menuItemRender={(item: any, dom: HTMLElement) => {
          if (item.isUrl || item.children || !item.path) {
            return dom;
          }
          return <Link to={item.path} onClick={()=> {
            // 把顶部搜索内容清除
            setGlobal({
              query:null,
              pageNos:undefined
            })
          }}>{dom}</Link>;
        }}
        breadcrumbRender={(routers = []) => [
          {
            path: '/',
            breadcrumbName: '首页',
          },
          ...routers,
        ]}
        menuDataRender={(menuList: any) => menuDataRender(menuList, global?.isGalaxyAdmin || false)}
        rightContentRender={() => <Header collapsed={collapsed} onCollapsedChange={() => setCollapsed(!collapsed)} />}
        {...rest}
      >
        <PageContainer header={{ title: null, breadcrumb: null }}>{children}</PageContainer>
      </ProLayout>
    </div>
  );
};

// 这里声明全局使用的所有状态：
const GlobalState = {
  currentUser: {},
};

const Root = (props: any) => (
  <Store value={GlobalState}>
    <BasicLayout {...props} />
  </Store>
);

export default Root;
