import React, { useState, useEffect } from 'react';
import { message, Form, Card, Button, Input, Table, Select, Alert } from 'antd';
import { useSet } from '@/utils/hooks';
import { getActiveType } from '@/utils/utils';
import { LeftOutlined } from '@ant-design/icons';
import { ColumnProps } from 'antd/es/table';
import { history } from 'umi';
import './index.less';

import { filterProps } from './interface';
import api from '@/api';
const { Option } = Select;

const FilterQuery = (props:any) => {
  const [form] = Form.useForm();
  const { TextArea } = Input;

  const [state, setState] = useSet({
    loading: false,
    dataSource: [],
    activeName: '',
    activeType:''
  });

  const { loading, dataSource, activeName, activeType } = state;

  const { location } = props;
  const { activityId } = location.query;

  useEffect(()=> {
    if (activityId) {
      const data = {
        id:activityId,
        fetch: false
      }
      api.activity.getActivity(data).then((res: any) => {
        if (res.code === '200' && res.data && res.data.activity) {
          if (res.data.activity) {
            form.setFieldsValue({
              activityId,
            })
            setState({
              activeName:res.data.activity?.activityTitle,
              activeType:res.data.activity?.dataSource
            })
          }
        } else {
          message.error(res.msg)
        }
      })
    }
  },[])

  const onFinish = (values: any) => {
    const { activityId, itemIds, type } = values;
    setState({
      loading: true,
      dataSource: [],
    });
    api.activity
      .QueryFilterReason({
        activityId,
        itemIds,
        type,
      })
      .then((res: any) => {
        if (res.code === '200') {
          setState({
            dataSource: res.data,
            loading: false,
          });
        } else {
          setState({
            dataSource: [],
            loading: false,
          });
          message.error(res.msg);
        }
      });
  };

  // 合并单元格
  const mergeColls = (text: any, array: any, col: any, index: number) => {
    //与上一个进行比较
    if (index !== 0 && text === array[index - 1][col]) {
      return 0;
    }

    //与下一个进行比较
    let rowSpan = 1;
    for (let i = index + 1; i < array.length; i++) {
      if (text !== array[i][col]) {
        break;
      }
      rowSpan++;
    }
    return rowSpan;
  };

  const columns: ColumnProps<filterProps>[] = [
    {
      title: '过滤原因',
      dataIndex: 'reason',
      key: 'reason',
      render: (text: any, record: any, index: number) => {
        const obj = {
          children: text,
          props: {
            rowSpan: 0,
          },
        };
        obj.props.rowSpan = mergeColls(record.reason, dataSource, 'reason', index);
        return obj;
      },
    },
    {
      title: '商品ID',
      dataIndex: 'itemId',
      key: 'itemId',
      render: (text: any, record: any, index: number) => {
        return record.itemId ? record.itemId : '-';
      },
    },
    {
      title: '备注',
      dataIndex: 'msg',
      key: 'msg',
      render: (text: any, record: any, index: number) => {
        return record.msg ? record.msg : '-';
      },
    },
  ];

  
  const onActivityId = () => {
    if (form.getFieldValue('activityId')) {
      const data = {
        id:form.getFieldValue('activityId'),
        fetch: false
      }
      api.activity.getActivity(data).then((res: any) => {
        if (res.code === '200') {
          if (form.getFieldValue('activityId') && res.data && res.data?.activity) {
            setState({
              activeName:res.data.activity?.activityTitle,
              activeType:res.data.activity?.dataSource
            })
          } else {
            setState({
              activeName:'',
              activeType:''
            })
          }
        } else {
          message.error(res.msg)
        }
      })
    } else {
      setState({
        activeName:'',
        activeType:''
      })
    }
  }

  return (
    <>
      <Card style={{ marginBottom: '20px' }} bordered={false}>
        {/* <Button
          type="link"
          icon={<LeftOutlined />}
          onClick={() => {
            history.push('/createCollect');
          }}
        >
          过滤查询
        </Button> */}
        <div className="flex">
          <div style={{ width: '60%' }}>
            <Form labelCol={{ span: 5 }} wrapperCol={{ span: 19 }} onFinish={onFinish} form={form}>
              <Form.Item label="品池ID" name="activityId">
                <Input placeholder="请输入品池ID" onChange={() => {onActivityId()}}/>
              </Form.Item>
              <Form.Item label="品池名称">
                <span className="ant-form-text">{activeName || '无'}</span>
              </Form.Item>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.activityId !== currentValues.activityId}
              >
                {({ getFieldValue }) =>
                  getFieldValue('activityId') ? (
                    <Form.Item label="选品物料类型">
                      <span className="ant-form-text">{getActiveType(activeType) || '无'}</span>
                    </Form.Item>
                  ) : null
                  }
              </Form.Item>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.activityId !== currentValues.activityId}
              >
                {({ getFieldValue }) =>
                  !getFieldValue('activityId') ? (
                    <Form.Item label="选品物料类型" name="type" rules={[{ required: true, message: '请选择商品类型' }]}>
                      <Select placeholder="请选择商品类型">
                        <Option value="HOTEL">酒店</Option>
                        <Option value="TRAVEL">商品</Option>
                        <Option value="POI">POI</Option>
                        <Option value="SHOP">商家</Option>
                        <Option value="BNB">民宿</Option>
                        <Option value="GLB">哥伦布榜单</Option>
                      </Select>
                    </Form.Item>
                  ) : null
                }
              </Form.Item>
              <Form.Item label="选品物料ID" name="itemIds" rules={[{ required: true, message: '请输入商品ID' }]}>
                <TextArea
                  maxLength={1300}
                  autoSize={{ minRows: 6, maxRows: 6 }}
                  placeholder="支持输入商品ID（iteam ID）或日历房ID （shid），多个英文逗号隔开示例：237657,78766,77665"
                />
              </Form.Item>
              <Form.Item wrapperCol={{ offset: 12, span: 16 }}>
                <Button type="primary" htmlType="submit" shape="round">
                  查询
                </Button>
              </Form.Item>
            </Form>
          </div>
          <div style={{ width: '40%' }} className='file_box'>
            <h2>使用文档：</h2>
            <p>哪个活动哪个品应该选出但没有选出</p>
            <p>填写选品池ID 和 货品ID(多个可用英文逗号分割开), 点击查询</p>
          </div>
        </div>
      </Card>
      <Card>
        <p>查看结果</p>
        <Table
          scroll={{ y: 500 }}
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey={(record) => record.itemId}
          pagination={false}
          bordered
        />
      </Card>
    </>
  );
};

export default FilterQuery;


