import React, { useState, useEffect } from 'react';
import { Card, Input, List, Modal, Table, Pagination, message, Space, Button, Menu, Dropdown } from 'antd';
import { get } from 'lodash';

import { GoodsDataSourceTypeEnum, CollectStatusEnum } from '@/pages/CreateCollect/constants';

import { updateHash, getUrlParams, filterCheckName, getGLBType } from '@/utils/utils';
import { useGlobal } from '@ali/use-global';
import { history } from 'umi';
import { tripTypeMap } from '@/pages/CreateCollect/components/StepTwoGoodsFilter/Preview/common';

import api from '@/api';

import PreviewRenderItem from '@/pages/CreateCollect/components/StepTwoGoodsFilter/Preview/previewRenderItem';

import './index.less';

const { Search } = Input;

interface PreviewProps {
  dataSource?: GoodsDataSourceTypeEnum;
  searchResult: any;
  ruleGroup: string;
  createType?: any;
  activity: any;
}

const generateItemUrl = (itemId: number) => `https://items.fliggy.com/item.htm?id=${itemId}`;

const Preview = (props: PreviewProps) => {
  const params = getUrlParams();
  const [global, setGlobal] = useGlobal();

  const { dataSource, searchResult, ruleGroup, createType, activity } = props;

  const [previewList, setPreviewList] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [sortType, setSortType] = useState('');

  const [loading, setLoading] = useState(false);
  const [updateTag, setUpdateTag] = useState(false);

  useEffect(() => {
    // 页面初始化的时候，使用外部获取的数据
    if (searchResult.data) {
      setPreviewList(searchResult.data);
      setTotal(searchResult.total);
    }
  }, [searchResult && searchResult.data]);

  useEffect(() => {
    if (updateTag) {
      fetchPreview(sortType, pageNo);
    }
  }, [updateTag]);

  // 权限控制
  const isPower = () => {
    let temp: any[] = [];
    get(activity, 'activityOwner', '')
      .split(',')
      .forEach((ele: any) => {
        if (ele.match(/\[(.+?)\]/g)) {
          ele.match(/\[(.+?)\]/g);
          temp.push(RegExp.$1);
        } else {
          temp.push(ele);
        }
      });
    if (temp.includes(get(global, 'createOwner.empId', '')) || temp.includes(get(global, 'createOwner.nick', ''))) {
      return true;
    }
    return false;
  };

  const previewFetchCallback = (res: any) => {
    setUpdateTag(false);
    if (res.code === '200' && res.msg === '操作成功') {
      setLoading(false);
      const data = get(res, 'data.pageResult.data');
      // console.log(data[0]);
      setPreviewList(get(res, 'data.searchResult.data') || []);
      setTotal(res.data.searchResult.total);
    } else {
      message.error(res.msg);
      setPreviewList([]);
      setTotal(0);
    }
  };

  const fetchPreview = (sortTypes: any, pageNo: any) => {
    setLoading(true);
    let temp: any;

    if (sortTypes === 'tagSortPre') {
      temp = {
        id: params.collectionId,
        ruleGroup,
        pageNo,
        pageSize,
        fetch: true,
        extQuery: JSON.stringify({
          query: searchText,
          sort: 'relScore',
          sortType: 'asc',
        }),
      };
    } else {
      temp = {
        id: params.collectionId,
        ruleGroup,
        pageNo,
        pageSize,
        fetch: true,
        extQuery: JSON.stringify({
          query: searchText,
          sort: sortTypes,
        }),
      };
    }
    api.activity.getActivity(temp).then(previewFetchCallback);
  };

  // 输入框的placehoder
  const searchPlacehoder = (dataSource: any) => {
    if (dataSource === 'HOTEL') {
      return '请输入酒店ID/名称';
    }
    if (dataSource === 'TRAVEL') {
      return '请输入卖家昵称或商品ID/标题';
    }

    if (dataSource === 'SPU') {
      return '请输入SPU';
    }

    return '请输入商品ID';
  };

  const menu = (
    <Menu>
      <Menu.Item
        key="1"
        onClick={() => {
          exportItem();
        }}
      >
        导出excel
      </Menu.Item>
      <Menu.Item key="2">
        <a target="_blank" href="https://yuque.antfin.com/docs/share/b2d44934-1c79-413d-b22f-49e121dd533d">
          导出odps
        </a>
      </Menu.Item>
    </Menu>
  );

  // 导出
  const exportItem = () => {
    const data = {
      activityIds: params.collectionId,
      itemCount: activity.itemCount ? activity.itemCount : undefined,
      type: 'single',
    };

    api.activity.exportItem(data).then((res: any) => {
      if (res.code === '200' && res.data) {
        message.success(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  // 选品结果样式
  const poolResult = (collectionId: any, activityId: any, activityTitle: any) => {
    return (
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <h3>{`选品结果——选品池ID：${collectionId}`}</h3>
        {window.location.href.includes('isFrame=true') ? (
          <Button
            type="primary"
            onClick={() => {
              window.open(
                `https://${window.location.host}/#/manageCollect/manageChild?activityTitle=${activityTitle}&id=${activityId}&isChild=1&isOwner=false&pageNos=1&isFrame=true`,
                '_self',
              );
            }}
          >
            返回选品池列表
          </Button>
        ) : (
          <div></div>
        )}
      </div>
    );
  };

  const pagination = {
    total: dataSource !== 'FLIGHT' ? total : previewList?.length || 0,
    pageSize,
    pageNo,
    showSizeChanger: false,
    onChange: (newPage: any) => {
      setPageNo(newPage);
      if (dataSource !== 'FLIGHT') {
        setUpdateTag(true);
      }
    },
  };

  const sortBtn = () => {
    switch (dataSource) {
      case 'LP':
      case 'HOTEL':
      case 'SHOP':
      case 'POI':
      case 'BNB':
      case 'FLIGHT':
      case 'SPU':
        return null;
      default:
        return (
          <Space style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 20, marginBottom: 20 }}>
            <Button
              onClick={() => {
                fetchPreview('', 1);
                setSortType('');
                setPageNo(1);
              }}
            >
              综合排序
            </Button>
            <Button
              onClick={() => {
                fetchPreview('soldRecent', 1);
                setSortType('soldRecent');
                setPageNo(1);
              }}
            >
              销量从高到低
            </Button>
            <Button
              onClick={() => {
                fetchPreview('reservePrice', 1);
                setSortType('reservePrice');
                setPageNo(1);
              }}
            >
              价格从高到低
            </Button>
          </Space>
        );
    }
  };

  return (
    <div className="filter-result">
      <Card title={poolResult(params.collectionId, params?.activityId, params?.activityTitle)} bordered={false}>
        <div className="row">
          <div>
            筛选出{total}个{filterCheckName(dataSource)}
          </div>
          <Search
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onSearch={() => {
              // setUpdateTag(true);
              fetchPreview('', 1);
              setSortType('');
              setPageNo(1);
            }}
            placeholder={searchPlacehoder(dataSource)}
            style={{ width: 300 }}
          />
        </div>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <Button
              type="primary"
              style={{ marginRight: '20px' }}
              disabled={!isPower()}
              onClick={() => {
                if (window.location.href.includes('isFrame=true')) {
                  history.push(
                    `/createCollect?type=${Number(createType) + 1}&step=2&collectionId=${
                      params.collectionId
                    }&isFrame=true`,
                  );
                } else {
                  history.push(
                    `/createCollect?type=${Number(createType) + 1}&step=2&collectionId=${
                      params.collectionId
                    }&isEdit=true`,
                  );
                }
              }}
            >
              修改选品规则
            </Button>
            <Dropdown overlay={menu} placement="bottomLeft" disabled={!isPower()}>
              <Button>导出</Button>
            </Dropdown>
            {!isPower() && (
              <span style={{ color: 'red', marginLeft: '20px' }}>如需开通编辑权限，请联系品池的创建人</span>
            )}
          </div>
          {sortBtn()}
        </div>

        <List
          style={{ marginTop: 20 }}
          loading={loading}
          grid={{
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 3,
            lg: 3,
            xl: 4,
            xxl: 5,
          }}
          dataSource={previewList}
          renderItem={(item) => (
            <PreviewRenderItem
              items={item}
              dataSource={dataSource}
              generateItemUrl={generateItemUrl}
              GoodsDataSourceTypeEnum={GoodsDataSourceTypeEnum}
              tripTypeMap={tripTypeMap}
              getGLBType={getGLBType}
              type="online"
            />
          )}
          pagination={total > pageSize ? pagination : false}
        />
      </Card>
    </div>
  );
};

export default Preview;
