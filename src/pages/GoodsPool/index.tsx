import { useState, useEffect } from 'react';
import { message, Spin } from 'antd';

import CreateSteps from '../CreateCollect/components/CreateSteps';
import Preview from './Preview';

import { getUrlParams } from '@/utils/utils';
import { CollectStatusEnum } from '../CreateCollect/constants';

import api from '@/api';

const GoodsPool = () => {
  const [searchResult, setSearchResult] = useState<any>({});
  const [activity, setActivity] = useState<any>();

  const urlParams = getUrlParams();

  useEffect(() => {
    if (urlParams.collectionId) {
      api.activity.getActivity({ id: urlParams.collectionId, fetch: true }).then((res) => {
        if (res.code === '200' && res.msg === '操作成功') {
          setActivity(res.data.activity);
          setSearchResult(res.data.searchResult);
        } else {
          message.error(`操作失败，错误信息：${res.msg}`);
        }
      });
    }
  }, [urlParams.collectionId]);

  let ruleGroup = activity?.ruleGroup;

  //如果ruleMap有内容（非空数组），则构造新规则
  if (JSON.parse(activity?.ruleMap || '[]').length > 0) {
    ruleGroup = JSON.stringify([
      {
        ruleConfigList: JSON.parse(activity?.ruleMap),
        ruleOp: 'AND',
      },
    ]);
  }

  return (
    <div>
      <CreateSteps current={CollectStatusEnum.FINISH} isShowTag={activity?.hasTag}/>
      {activity ? (
        <Preview searchResult={searchResult} dataSource={activity.dataSource} ruleGroup={ruleGroup} createType={activity.createType} activity={activity}/>
      ) : (
        <Spin style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginTop: '100px' }} />
      )}
    </div>
  );
};

export default GoodsPool;
