// 该第一步创建选品集已经废弃

import { useState, useEffect } from 'react';
import { Button, Radio, Input, Select, DatePicker, message } from 'antd';
import moment from 'moment';

import CollectSearch from '@/components/CollectSearch';
import UserSearch from '@/components/UserSearch';

import { updateHash, getUrlParams } from '@/utils/utils';
import { ActivityTypeEnum, UpdateTimeTypeEnum, CollectStatusEnum } from '../../constants';
import api from '@/api';

import './index.less';

const RequiredStar = <span style={{ color: 'red' }}>*</span>;

const StepOneCreateCollection = () => {
  const [formData, setFormData] = useState({
    activityType: ActivityTypeEnum.NewActivity,
    newActivityName: '', // 新建的活动，需要输入新建的名称
    oldActivityName: -1, // 已存在的活动，用的是id,默认是全部
    activityOwner: [] as string[], //活动管理员
    collectionName: '', //品集名称
    collectionOwner: [] as string[], //品集管理员
    goodsType: '', // 货品类型
    expireTime: '', // 过期时间
    hasTag: false, // 默认打标
    updateTime: UpdateTimeTypeEnum.IMMUATE, // 默认不更新
  });
  const [goodTypeOptions, setGoodTypeOptions] = useState<{ label: string; value: number }[]>([]);

  const urlParams = getUrlParams();

  useEffect(() => {
    api.activity.dataSourceList().then((res) => {
      const temp = Object.keys(res)
        .filter((key) => res[key] !== 'RP' && res[key] !== 'FLIGHT' && res[key] !== 'LP') // 过滤掉酒店日历房RP选项、机票、LP页面
        .map((e) => {
          return {
            label: e,
            value: res[e],
          };
        });
      setGoodTypeOptions(temp);
    });
  }, []);

  useEffect(() => {
    // 如果url中带有activityId，则自动填写活动信息
    if (urlParams.activityId) {
      api.activity.getActivity({ id: urlParams.activityId, fetch: false }).then((res) => {
        setFormData({
          ...formData,
          activityType: ActivityTypeEnum.OldActivity,
          oldActivityName: Number(urlParams.activityId),
          activityOwner: res.data.activity.activityOwner.split(','),
        });
      });
    }
  }, [urlParams.activityId]);

  const onActivityTypeChange = (activityType: ActivityTypeEnum) => {
    setFormData({
      ...formData,
      activityType,
    });
  };

  const jumpToNextStep = (collectionId: string) => {
    // 跳转到下一步
    updateHash({
      params: {
        type: urlParams.type, // 选品类型FilterType
        step: CollectStatusEnum.SETCOLLECTCONDITION,
        collectionId, // 创建成功的品集id
      },
    });
  };

  const addChildCollectionToExistActivity = async () => {
    if (formData.activityOwner.length === 0) {
      message.error('请输入选品人员');
      return;
    }
    if (!formData.collectionName) {
      message.error('请输入品集名称');
      return;
    }
    if (formData.collectionOwner.length === 0) {
      message.error('请输入选品人员');
      return;
    }
    if (!formData.goodsType) {
      message.error('请输入货品类型');
      return;
    }
    if (!formData.expireTime) {
      message.error('请输入过期时间');
      return;
    }
    const temp = {
      activityTitle: formData.collectionName, // 品集名称
      expireTime: formData.expireTime,
      parentId: formData.oldActivityName, // 主活动id,这个需要备注一下
      activityOwner: formData.collectionOwner,
      dataSource: formData.goodsType, // 货品类型
      hasTag: formData.hasTag ? 1 : 0, // 转成0，1
      // deleteTagWay 没有要求提交
      updateType: formData.updateTime, // 更新时间
      createType: Number(urlParams.type) - 1,
    };
    const res = await api.activity.save(temp);
    if (res.code === '200' && res.msg === '操作成功') {
      message.success('品集创建成功！');
      jumpToNextStep(res.data);
    } else {
      message.error(res.msg);
    }
  };

  const addParentActivityAndChildCollection = async () => {
    // console.log('values', values);
    // 保存主活动

    if (!formData.newActivityName) {
      message.error('请输入活动名称');
      return;
    }
    if (formData.activityOwner.length === 0) {
      message.error('请输入选品人员');
      return;
    }
    if (!formData.collectionName) {
      message.error('请输入品集名称');
      return;
    }
    if (formData.collectionOwner.length === 0) {
      message.error('选品人员');
      return;
    }
    if (!formData.goodsType) {
      message.error('请输入货品类型');
      return;
    }
    if (!formData.expireTime) {
      message.error('请输入过期时间');
      return;
    }
    const res1 = await api.activity.save({
      activityTitle: formData.newActivityName,
      activityOwner: formData.activityOwner,
    });
    if (res1.code === '200' && res1.msg === '操作成功') {
      const temp = {
        activityTitle: formData.collectionName, // 品集名称
        expireTime: formData.expireTime,
        parentId: res1.data, // 主活动id,这个需要备注一下
        activityOwner: formData.collectionOwner,
        dataSource: formData.goodsType, // 货品类型
        hasTag: formData.hasTag ? 1 : 0, // 转成0，1
        // deleteTagWay 没有要求提交
        updateType: formData.updateTime, // 更新时间
        createType: Number(urlParams.type) - 1,
        // checkDisabled // 后台未用到
      };
      const res2 = await api.activity.save(temp);
      if (res2.code === '200' && res2.msg === '操作成功') {
        message.success('品集创建成功！');
        // 跳转到下一步
        jumpToNextStep(res2.data);
      } else {
        message.error(res2.msg);
      }
    } else {
      message.error(res1.msg);
    }
  };

  /**
   * 新建活动需要调用两个接口，使用已创建的活动只要调用一个接口
   */
  const submit = () => {
    // console.log('提交');
    // 保存主活动
    // 如果是已创建的活动，只需要请求
    if (formData.activityType === ActivityTypeEnum.OldActivity) {
      addChildCollectionToExistActivity();
    }
    // 如果是创建新活动，要先保存主活动，再保存品集（子活动）
    if (formData.activityType === ActivityTypeEnum.NewActivity) {
      addParentActivityAndChildCollection();
    }
  };

  // 时间过期
  const disabledDate = (current: any) => {
    return current < moment().startOf('day') || current > moment() + 365 * 24 * 60 * 60 * 1000;
  };

  const renderItem = ({
    title,
    key,
    isRequired,
    content,
  }: {
    title: string;
    key: string;
    isRequired: boolean;
    content: any;
  }) => {
    return (
      <div className="row" key={key}>
        <div className="form-title">
          {isRequired && RequiredStar}
          {title}:
        </div>
        <div className="form-content">{content}</div>
      </div>
    );
  };

  const basisFormItems = [
    {
      title: '活动类型',
      isRequired: true,
      key: 'activityType',
      content: (
        <Radio.Group
          value={formData.activityType}
          onChange={(e) => {
            onActivityTypeChange(e.target.value);
          }}
        >
          <Radio value={ActivityTypeEnum.NewActivity}>新建活动</Radio>
          <Radio value={ActivityTypeEnum.OldActivity}>已创建的活动</Radio>
        </Radio.Group>
      ),
    },
    {
      title: '活动名称',
      isRequired: true,
      key: 'activityName',
      content:
        formData.activityType === ActivityTypeEnum.NewActivity ? (
          <Input
            style={{ width: '100%' }}
            value={formData.newActivityName}
            onChange={(e) => {
              setFormData({
                ...formData,
                newActivityName: e.target.value,
              });
            }}
          />
        ) : (
          <CollectSearch
            style={{ width: '100%' }}
            hasAll
            value={formData.oldActivityName}
            onChange={(val: number, item: any) => {
              // 从item中获取人员信息，并填入表单中
              if (val === -1) {
                setFormData({
                  ...formData,
                  newActivityName: '',
                  oldActivityName: -1,
                  activityOwner: ['暂无'],
                });
              } else {
                setFormData({
                  ...formData,
                  newActivityName: '',
                  oldActivityName: val,
                  activityOwner: item.option.activityOwner?.split(','),
                });
              }
            }}
          />
        ),
    },
    {
      title: '选品人员',
      isRequired: true,
      key: 'activityOwner',
      content:
        formData.activityType === ActivityTypeEnum.NewActivity ? (
          <UserSearch
            mode="multiple"
            value={formData.activityOwner}
            onChange={(e: any) => {
              setFormData({
                ...formData,
                activityOwner: e,
              });
            }}
            style={{ width: '100%' }}
          />
        ) : (
          <div>{formData.activityOwner?.join(',')}</div>
        ),
    },
  ];

  const collectionFormItems = [
    {
      title: '品集名称',
      isRequired: true,
      key: 'collectionName',
      content: (
        <Input
          value={formData.collectionName}
          onChange={(e) => {
            setFormData({
              ...formData,
              collectionName: e.target.value,
            });
          }}
        />
      ),
    },
    {
      title: '选品人员',
      isRequired: true,
      key: 'collectionOwner',
      content: (
        <UserSearch
          mode="multiple"
          value={formData.collectionOwner}
          onChange={(e: any) => {
            setFormData({
              ...formData,
              collectionOwner: e,
            });
          }}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '货品类型',
      isRequired: true,
      key: 'goodsType',
      content: (
        <Select
          options={goodTypeOptions}
          value={formData.goodsType}
          onChange={(e) => {
            setFormData({
              ...formData,
              goodsType: e,
            });
          }}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '过期时间',
      isRequired: true,
      key: 'expireTime',
      content: (
        <DatePicker
          showTime
          disabledDate={disabledDate}
          value={!formData.expireTime ? null : moment(formData.expireTime)}
          onChange={(e) => {
            setFormData({
              ...formData,
              expireTime: moment(e).format('YYYY-MM-DD HH:mm:ss'),
            });
          }}
        />
      ),
    },
    {
      title: '是否打标',
      isRequired: true,
      key: 'hasTag',
      content:
        formData.goodsType === 'SHOP' || formData.goodsType === 'BNB' ? (
          <Radio.Group
            value={formData.hasTag}
            onChange={(e) => {
              setFormData({
                ...formData,
                hasTag: e.target.value,
              });
            }}
          >
            <Radio value={false}>否</Radio>
          </Radio.Group>
        ) : (
          <Radio.Group
            value={formData.hasTag}
            onChange={(e) => {
              setFormData({
                ...formData,
                hasTag: e.target.value,
              });
            }}
          >
            <Radio value={true}>是</Radio>
            <Radio value={false}>否</Radio>
          </Radio.Group>
        ),
    },
    {
      title: '更新时间',
      isRequired: true,
      key: 'updateTime',
      content: (
        <Select
          style={{ width: '100%' }}
          disabled={urlParams.type === '2'}
          options={[
            { label: '每天更新（每天下午3点）', value: UpdateTimeTypeEnum.EVERYDAY },
            // { label: '时刻更新（准点时刻更新）', value: UpdateTimeTypeEnum.EVERYHOUR },
            // { label: '小时更新（创建时间为更新时刻）', value: UpdateTimeTypeEnum.EVERYMOMENT },
            { label: '不更新（品集数据不会变化）', value: UpdateTimeTypeEnum.IMMUATE },
          ]}
          value={formData.updateTime}
          onChange={(e) => {
            setFormData({
              ...formData,
              updateTime: e,
            });
          }}
        />
      ),
    },
  ];

  return (
    <div className="create-form">
      <div className="form-container">
        <div className="row">
          <h2>选品集信息</h2>
        </div>
        {basisFormItems.map(renderItem)}
        <div className="row">
          <h2>选品池信息</h2>
        </div>
        {collectionFormItems.map(renderItem)}
        <span style={{ color: 'red', marginLeft: '90px' }}>
          时刻更新和小时更新不要乱选。 选品池选品条件底层数据满足实时更新才生效，否则视为不更新
        </span>
      </div>
      <div style={{ display: 'flex', flexDirection: 'row-reverse' }}>
        <Button
          type="primary"
          onClick={() => {
            submit();
          }}
        >
          下一步
        </Button>
      </div>
    </div>
  );
};

export default StepOneCreateCollection;
