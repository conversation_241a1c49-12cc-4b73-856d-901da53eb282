import React, { useState, useEffect } from 'react';
import { Select, Space, Radio, Card, Spin, InputNumber, Button } from 'antd';
import { useGlobal } from '@ali/use-global';
import { get, cloneDeep } from 'lodash';
import { Link, history } from 'umi';

import RulesBox from './RulesBox';
import Preview from './Preview';
import UploadFiles from './UploadFiles';
import ExistSelect from './UploadFiles/ExistSelect';
import ModalRecord from '@/components/ModalRecord';

import { FilterType, GoodsDataSourceTypeEnum } from '../../constants';
import { getReactKey } from '@/utils/utils';

import './index.less';

const StepTwoGoodsFilter = (props: { filterType: FilterType }) => {
  const { filterType } = props;
  const [countType, setCountType] = useState('defalut');
  const [uploadType, setUploadType] = useState<'excel' | 'odps' | 'existCollection' | ''>('');
  const [rules, setRules] = useState<any[]>([]); // 新的规则（有规则组概念）
  const [dataSource, setDataSource] = useState(''); // 货品来源
  const [hasTag, setHasTag] = useState(0);
  // 用来存放上传excel/odps之后返回的规则，要加入到每个规则组中去
  const [uploadRule, setUploadRule] = useState<any>();

  const [loading, setLoading] = useState(false);
  const [limitCount, setLimitCount] = useState<number>();
  // 是否需要自动请求展示预览的选品池，目前是已经存在规则了的需要自动请求
  const [needAutoFetchPreview, setNeedAutoFetchPreview] = useState(false);
  // 是否第一次上传
  const [isUpload, setIsUpload] = useState(true);
  // 盘点现有品集
  const [existVal, setExistVal] = useState<number>();

  //历史记录版本
  const [isRecordVisible, setIsRecordVisible] = useState(false);

  //判断从哪里进到历史记录
  const [isRule, setIsRule] = useState(true);

  const [global] = useGlobal();

  useEffect(() => {
    if (global?.activity?.id) {
      console.log('global?.activity', global?.activity);
      const ruleGroup = JSON.parse(global?.activity?.ruleGroup || '[]');
      if (global?.activity?.limitCount) {
        setCountType('custom');
        setLimitCount(global?.activity?.limitCount);
      }
      // 如果存在createType字段，并且为0
      if (global?.activity.createType === Number(FilterType.FreeSelect) - 1) {
        // console.log('ruleGroup', ruleGroup);
        const ruleMap = JSON.parse(global?.activity.ruleMap);
        let preCompose = [];

        //这个是线上之前的子活动数据，数据字段包含老的规则（ruleMap），新的字段（ruleGroup）为空数组字符串
        if (ruleMap.length > 0 && ruleGroup.length === 0) {
          preCompose = [
            {
              ruleConfigList: ruleMap,
            },
          ];
        }
        // 俩都是空的，认为就是新创建的
        if (ruleMap.length === 0 && ruleGroup.length === 0) {
          preCompose = [];
        }
        // ruleGroup中有，而ruleMap无
        if (ruleMap.length === 0 && ruleGroup.length > 0) {
          preCompose = ruleGroup;
        }

        // 给rule加上reactKey
        const temp = preCompose.map((rule: any, index: number) => {
          const { ruleConfigList, ...other } = rule;
          return {
            ...other,
            ruleConfigList: ruleConfigList.map((conf: any, idx: number) => {
              return {
                ...conf,
                reactKey: `${getReactKey()}-${index}-${idx}`,
              };
            }),
            reactKey: `${getReactKey()}-${index}`,
          };
        });

        if (temp.length > 0) {
          setNeedAutoFetchPreview(true);
        }
        setRules(temp);
      }
      // 上传品集
      if (global?.activity.createType === Number(FilterType.Upload) - 1) {
        setRules([]);
        // 如果类型是excel
        if (global?.activity?.uploadType === 'EXCEL') {
          setUploadType('excel');
          // 如果已经上传过excel，则把这个弄出来
          if (ruleGroup.length > 0) {
            // 这个代表上传的规则，要放到所有的规则组里面去
            setRules(ruleGroup);
            setNeedAutoFetchPreview(true);
          }
        }
        // 如果类型是odps
        if (global?.activity?.uploadType === 'ODPS') {
          setUploadType('odps');
          // 如果已经上传过excel，则把这个弄出来
          if (ruleGroup.length > 0) {
            setRules(ruleGroup);
            setNeedAutoFetchPreview(true);
          }
        }
      }
      // 货品盘点
      if (global?.activity.createType === Number(FilterType.Inventory) - 1) {
        // 删除时要清空
        setUploadRule('');
        setRules([]);
        // 如果类型是excel
        if (global?.activity?.uploadType === 'EXCEL') {
          setUploadType('excel');
          // 如果已经上传过excel，则把这个弄出来
          if (Object.keys(ruleGroup).length > 0) {
            // 这个规则组中只有一个上传的规则，注意，这里的ruleGroup是数组 by 长弘，2021.09.13 星辰V2测试群
            setRules(ruleGroup);
            setNeedAutoFetchPreview(true);
            // 上传后返回的规则添加到每个规则组中，所以需要先把这个上传得到的规则保存下来
            const uploadRuleObj = ruleGroup[0].ruleConfigList?.filter((ele: any) => {
              if (ele.ruleName === 'selectionTagHidden') {
                return ele;
              }
            });
            setUploadRule(uploadRuleObj[0]);
          }
        }
        // 如果类型是odps
        if (global?.activity?.uploadType === 'ODPS') {
          setUploadType('odps');
          // 如果已经上传过excel，则把这个弄出来
          if (ruleGroup.length > 0) {
            // 这个规则组中只有一个上传的规则，注意，这里的ruleGroup是数组 by 长弘，2021.09.13 星辰V2测试群
            setRules(ruleGroup);
            setNeedAutoFetchPreview(true);
            // 上传后返回的规则添加到每个规则组中，所以需要先把这个上传得到的规则保存下来
            // const uploadRuleObj = get(ruleGroup, '[0].ruleConfigList[0]');
            const uploadRuleObj = ruleGroup[0].ruleConfigList?.filter((ele: any) => {
              if (ele.ruleName === 'selectionTagHidden') {
                return ele;
              }
            });
            setUploadRule(uploadRuleObj[0]);
          }
        }

        // 如果类型是盘点现有品集
        if (global?.activity?.uploadType !== 'EXCEL' && global?.activity?.uploadType !== 'ODPS') {
          setUploadType('existCollection');
          if (ruleGroup.length > 0) {
            const existVal = ruleGroup[0].ruleConfigList.filter((ele: any) => {
              if (ele.ruleName === 'selectionTagHidden') {
                return ele;
              }
            });
            setExistVal(Number(get(existVal, '[0].ruleValue').replace('tripgalaxy:', '')));

            setNeedAutoFetchPreview(true);
          }
        }
        if (ruleGroup.length > 0) {
          // 给rule加上reactKey
          const temp = ruleGroup.map((rule: any, index: number) => {
            const { ruleConfigList, ...other } = rule;
            return {
              ...other,
              ruleConfigList: ruleConfigList.map((conf: any, idx: number) => {
                return {
                  ...conf,
                  reactKey: `${getReactKey()}-${index}-${idx}`,
                };
              }),
              reactKey: `${getReactKey()}-${index}`,
            };
          });

          if (temp.length > 0) {
            setNeedAutoFetchPreview(true);
          }
          setRules(temp);
        }
      }
      // 公共部分
      // 更新货品来源
      setDataSource(get(global, 'activity.dataSource'));
      // 是否打标
      setHasTag(get(global, 'activity.hasTag'));
    } else {
      setRules([]);
      setDataSource('');
      setHasTag(0);
    }
  }, [global?.activity?.id, global?.activity?.ruleGroup]); // 上传了之后会重新查询activity，更新ruleGroup字段，所以要依赖这个字段的变化

  const renderRuleSelect = () => {
    // 操作rule的时候，要过滤掉表示上传的规则
    const temp = rules.map((e) => {
      // 从每个规则组中去掉上传的规则
      const { ruleConfigList, ...other } = e;

      const tesd = ruleConfigList.filter((ruleItem: any) => ruleItem.ruleName !== 'selectionTagHidden');

      return {
        ...other,
        ruleConfigList: tesd,
      };
    });

    return (
      <div className="row first-row">
        <div className="row-title">筛选规则：</div>
        <div className="row-content">
          <>
            <RulesBox
              uploadType={uploadType}
              filterType={filterType}
              existVal={existVal}
              createFlag={global?.activity?.createFlag}
              rules={temp}
              onChange={(data) => {
                const tempData = data.map((e: any) => {
                  const { ruleConfigList, ...other } = e;
                  // 只有在需要上传的时候，才会有这个，默认是undefined
                  if (uploadRule) {
                    return {
                      ...other,
                      ruleConfigList: [...ruleConfigList, uploadRule],
                    };
                  } else {
                    // 如果没有上传，则不需要加这个代表上传的规则
                    return e;
                  }
                });
                setRules(tempData);
              }}
              dataSource={dataSource as GoodsDataSourceTypeEnum}
            />
            <p>规则组内指标为交集（且的关系），即所有规则被全被选出才能被圈出；</p>
            <p>规则组之间为并集（或的关系），如符合规则组1的条件或符合规则组2的条件即可被圈出。</p>
          </>
        </div>
      </div>
    );
  };

  const renderRuleSort = () => {
    return (
      <div className="row">
        <div className="row-title">排序规则：</div>
        <div className="row-content">
          <Space>
            <Select
              style={{ width: 200 }}
              options={[
                { label: '1', value: 1 },
                { label: '2', value: 2 },
              ]}
            />
            <Select
              style={{ width: 200 }}
              options={[
                { label: '1', value: 1 },
                { label: '2', value: 2 },
              ]}
            />
          </Space>
        </div>
      </div>
    );
  };

  const renderCollectCount = () => {
    return (
      <div className="row">
        <div className="row-title">选品数量：</div>
        <div className="row-content">
          <Radio.Group
            onChange={(e) => {
              setCountType(e.target.value);
            }}
            value={countType}
          >
            <Radio value="defalut">默认</Radio>
            <Radio value="custom">
              <Space>
                自定义
                {countType === 'custom' && (
                  <InputNumber
                    value={limitCount}
                    onChange={(e) => {
                      setLimitCount(e);
                    }}
                  />
                )}
              </Space>
            </Radio>
          </Radio.Group>
        </div>
      </div>
    );
  };

  // 修改盘点现有品集时删除原来select
  const existValChange = (val: any) => {
    const temps = rules.map((e) => {
      // 从每个规则组中去掉上传的规则
      const { ruleConfigList, ...other } = e;

      const tesd = ruleConfigList.filter((ruleItem: any) => ruleItem.ruleName !== 'selectionTagHidden');

      return {
        ...other,
        ruleConfigList: tesd,
      };
    });

    setRules(cloneDeep(temps));
    setExistVal(val);
  };

  // 打开历史纪录
  const openRecord = () => {
    setIsRecordVisible(true);
  };

  // 关闭历史纪录
  const closeRecord = () => {
    setIsRecordVisible(false);
  };

  /** 自由选品 */
  const renderFreeSelect = () => {
    return (
      <div className="free-select">
        {renderRuleSelect()}
        {/* 自由选品暂时先去掉排序规则和选品数量 */}
        {/* {renderRuleSort()} */}
        {/* 数量先隐藏 */}
        {/* {renderCollectCount()} */}
      </div>
    );
  };

  const renderUploadRow = () => {
    return (
      <div className="row first-row">
        <div className="row-title">品集来源：</div>
        <div className="row-content">
          <Radio.Group
            style={{ marginBottom: 10 }}
            onChange={(e) => {
              // 切换时要清除规则组
              setRules([]);
              setUploadType(e.target.value);
            }}
            value={uploadType}
            // 返回的数据里有这个字段的话，就不能再操作了
            disabled={!!global?.activity?.uploadType}
          >
            <Radio value="excel">Excel上传</Radio>
            <Radio value="odps">Odps上传</Radio>
            {global?.activity.createType === Number(FilterType.Inventory) - 1 ? (
              <Radio value="existCollection">盘点现有品集</Radio>
            ) : null}
          </Radio.Group>
          {uploadType === 'odps' && (
            <a href="https://aliyuque.antfin.com/qnwrq9/kzl68s/ppra4i?singleDoc#" target="_blank">
              ODPS如何授权
            </a>
          )}
          <UploadFiles
            uploadType={uploadType}
            setLoading={setLoading}
            setIsUpload={setIsUpload}
            existSelect={
              <ExistSelect
                value={existVal}
                onChange={(val: any) => existValChange(val)}
                acDataSource={get(global, 'activity.dataSource')}
              />
            }
          />
        </div>
      </div>
    );
  };

  const renderInventory = () => {
    return (
      <div className="inventory">
        {renderUploadRow()}
        {renderRuleSelect()}
        {/* {renderRuleSort()} */}
        {/* 数量先隐藏 */}
        {/* {renderCollectCount()} */}
      </div>
    );
  };

  let filterTitle = '自由选品';

  if (filterType === FilterType.Upload) {
    filterTitle = '上传品集';
  }
  if (filterType === FilterType.Inventory) {
    filterTitle = '盘点货品';
  }

  const renderPreview = () => {
    let temp = rules;
    // 上传excel之后，走的是这个逻辑，rules的长度大于0
    if (rules.length > 0) {
      // 去除reactKey
      temp = rules.map((ruleGroupItem: any) => {
        const { ruleConfigList, ruleOp } = ruleGroupItem;

        return {
          ruleConfigList: ruleConfigList.map((ruleConfig: any) => {
            const { reactKey, ...other } = ruleConfig;
            return other;
          }),
          ruleOp,
        };
      });
    }

    // 如果是盘点，并且没有选规则组，则提交上传得到的规则组
    if (rules.length === 0 && uploadRule) {
      temp = [
        {
          ruleOp: 'AND',
          ruleConfigList: [uploadRule],
        },
      ];
    }

    // 如果是盘点现有品集，拼接规则
    if (existVal) {
      let newExisRule = {
        canDelete: true,
        id: 376,
        propType: '3000',
        ruleDisplayName: '根据星辰id选品',
        ruleDom: 'INPUT',
        ruleName: 'selectionTagHidden',
        ruleOp: 'EQUAL',
        ruleValue: `tripgalaxy:${existVal}`,
        ruleValueType: 'VARCHAR',
        valueTemplate: '举例:tripgalaxy:123234  %s',
      };
      if (rules.length === 0) {
        temp = [
          {
            ruleOp: 'AND',
            ruleConfigList: [newExisRule],
          },
        ];
      } else {
        let existTemp = rules.map((e) => {
          // 从每个规则组中去掉上传的规则
          const { ruleConfigList, ...other } = e;

          const tesd = ruleConfigList.filter((ruleItem: any) => ruleItem.ruleName !== 'selectionTagHidden');

          return {
            ...other,
            ruleConfigList: tesd,
          };
        });
        let tempList = existTemp.map((ele: any) => {
          const { ruleConfigList, ...other } = ele;
          return {
            ...other,
            ruleConfigList: [...ruleConfigList, newExisRule],
          };
        });

        // 清除reactkey
        temp = tempList.map((ruleGroupItem: any) => {
          const { ruleConfigList, ruleOp } = ruleGroupItem;

          return {
            ruleConfigList: ruleConfigList.map((ruleConfig: any) => {
              const { reactKey, ...other } = ruleConfig;
              return other;
            }),
            ruleOp,
          };
        });
      }
    }

    return (
      <Preview
        dataSource={dataSource as GoodsDataSourceTypeEnum}
        uploadType={uploadType}
        isUpload={isUpload}
        rules={temp}
        hasTag={hasTag}
        isCreatingCollect
        // 如果进入的时候rules的长度大于0，则自动请求商品池
        autoFirstFetch={needAutoFetchPreview}
        limitCount={limitCount}
        setNeedAutoFetchPreview={setNeedAutoFetchPreview}
      />
    );
  };

  return (
    <Spin spinning={loading}>
      <div className="goods-filter">
        <Card title={`${filterTitle}——选品池ID:${history?.location?.query?.collectionId}`} bordered={false}>
          <div className="rules-maker">
            {filterType === FilterType.FreeSelect && (
              <div style={{ marginBottom: '20px' }}>
                <Button
                  type="primary"
                  onClick={() => {
                    openRecord();
                  }}
                >
                  查看历史规则
                </Button>
                {isRecordVisible ? (
                  <ModalRecord
                    isRecordVisible={isRecordVisible}
                    closeRecord={closeRecord}
                    ids={history?.location?.query?.collectionId}
                    isRule={isRule}
                  />
                ) : null}
              </div>
            )}
            {filterType === FilterType.FreeSelect && renderFreeSelect()}
            {filterType === FilterType.Upload && <div className="upload">{renderUploadRow()}</div>}
            {filterType === FilterType.Inventory && renderInventory()}
          </div>
        </Card>
        {renderPreview()}
      </div>
    </Spin>
  );
};

export default StepTwoGoodsFilter;
