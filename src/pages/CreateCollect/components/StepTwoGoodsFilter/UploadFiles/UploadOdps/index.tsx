import React, { useEffect, useState } from 'react';
import { Input, Button, message, Form, Select, Spin, Tooltip, Modal, Alert, Typography } from 'antd';
import { useGlobal } from '@ali/use-global';

import { getUrlParams } from '@/utils/utils';

import api from '@/api';
import { debounce } from 'lodash';
import activity from '@/api/activity';

type UploadOdpsProps = {
  setLoading: (val: boolean) => void;
};

const { Paragraph } = Typography;

const { Option } = Select;

const UploadOdps = (props: UploadOdpsProps) => {
  const [form] = Form.useForm();
  const urlParams = getUrlParams();

  // 子活动id/品集id
  const collectionId = urlParams.collectionId;
  const isEdit = urlParams.isEdit || 'false';
  const [tableAuthConfig, setTableAuthConfig] = useState({
    tableOptions: [],
    loading: false,
  });
  const [primaryKeyOptions, setPrimaryKeyOptions] = useState([]);
  const [partitionOptions, setPartitionOptions] = useState([]);
  const [fieldLoading, setFieldLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  const [global, setGlobal] = useGlobal();

  const odpsConfig = global?.activity?.extInfo?.odpsConfig;

  useEffect(() => {
    if (odpsConfig && isEdit === 'true') {
      const { odpsConfig } = global.activity.extInfo;
      const tableGuid =
        'odps.' + (odpsConfig?.projectName ? odpsConfig?.projectName + '.' : '') + odpsConfig?.tableName;
      form.setFieldsValue({
        ...odpsConfig,
        tableGuid: tableGuid,
      });
    }
  }, [odpsConfig]);

  const handleSearch = (value: any) => {
    if (!value) return;
    setTableAuthConfig({
      tableOptions: [],
      loading: true,
    });
    api.odps.queryOdpsTable({ keyword: value }).then((res: any) => {
      if (res.code === '200' && res.data) {
        let result: any = [];
        Object.keys(res.data).forEach((key) => {
          result.push({
            label: key,
            value: key,
            desc: res.data[key],
          });
        });
        setTableAuthConfig({
          tableOptions: result,
          loading: false,
        });
      }
    });
  };

  const fetchFields = (value: string) => {
    api.odps.queryDwsTableColumn({ tableGuid: value }).then((res: any) => {
      if (res.code === '200' && res.data) {
        let result: any = [];
        Object.keys(res.data).forEach((key) => {
          result.push({
            label: key,
            value: key,
            desc: res.data[key],
          });
        });
        setPrimaryKeyOptions(result);
      }
    });
    api.odps.queryDwsTablePartition({ tableGuid: value }).then((res: any) => {
      if (res.code === '200' && res.data) {
        let result: any = [];
        Object.keys(res.data).forEach((key) => {
          result.push({
            label: key,
            value: key,
          });
        });
        form.setFieldValue('partition', result[0]?.value);
        setPartitionOptions(result);
      } else {
        setPartitionOptions([]);
      }
    });
  };

  const onFinish = (value: any) => {
    const data = {
      ...value,
      activityId: collectionId,
    };
    if (!data.whereCondition) {
      delete data.whereCondition;
    }
    setSubmitLoading(true);
    api.odps.createActivityByOdps(data).then((res: any) => {
      if (res.code === '200' && res.data) {
        api.activity.getActivity({ id: collectionId, fetch: false }).then((res: any) => {
          if (res.code === '200' && res.msg === '操作成功') {
            setGlobal({
              activity: res.data.activity,
              odpsResult: null,
            });
          }
        });
      } else {
        message.warning(res.msg || '上传失败');
      }
      setSubmitLoading(false);
    });
  };

  const checkOdpsTable = (value: string) => {
    setFieldLoading(true);
    const activity = global?.activity;
    if (odpsConfig) {
      setGlobal({
        activity: {
          ...activity,
          extInfo: {
            ...activity?.extInfo,
            odpsConfig: {
              projectName: value.split('.')[1],
              tableName: value.split('.')[2],
            },
          },
        },
      });
    }
    setPartitionOptions([]);
    api.odps.checkOdpsAuth({ tableGuid: value }).then((res: any) => {
      if (res.data === 'false') {
        message.warning('未授权');
        setFieldLoading(false);
      } else {
        message.success('校验通过');
        fetchFields(value);
        setFieldLoading(false);
      }
    });
  };

  const renderUploadForm = () => {
    return (
      <Form
        onFinish={onFinish}
        disabled={odpsConfig && !global?.odpsResult}
        form={form}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 14 }}
      >
        <Form.Item required label="ODPS表名">
          <Form.Item noStyle name="tableGuid" rules={[{ required: true, message: '请输入ODPS表名' }]}>
            <Select
              showSearch
              defaultActiveFirstOption={false}
              filterOption={false}
              style={{ marginRight: 16, width: '80%' }}
              placeholder="请输入您要搜索的ODPS表名"
              notFoundContent={tableAuthConfig.loading ? <Spin size="small" /> : null}
              onSearch={debounce(handleSearch, 500)}
              onChange={() => {
                form.setFieldsValue({
                  field: undefined,
                  whereCondition: undefined,
                  partition: undefined,
                });
              }}
            >
              {tableAuthConfig.tableOptions.map((item: any) => {
                return (
                  <Option value={item.value} key={item.value}>
                    <Tooltip
                      zIndex={10004}
                      autoAdjustOverflow
                      title={
                        <>
                          {item.label}
                          <br />
                          <span style={{ color: '#999999' }}>描述：{item.desc || '空'}</span>
                        </>
                      }
                    >
                      {item.label}
                      <br />
                      <span style={{ color: '#999999' }}>描述：{item.desc || '空'}</span>
                    </Tooltip>
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          {odpsConfig && (
            <Paragraph
              style={{ display: 'inline-block', marginBottom: 0, marginRight: 12 }}
              copyable={{
                text:
                  'odps.' + (odpsConfig?.projectName ? odpsConfig?.projectName + '.' : '') + odpsConfig?.tableName ||
                  '空',
              }}
            />
          )}
          <Button
            onClick={() => {
              const tableGuidValue = form.getFieldValue('tableGuid');
              if (!tableGuidValue) {
                message.warning('请先选择odps表');
                return;
              }
              checkOdpsTable(tableGuidValue);
            }}
            type="primary"
          >
            校验
          </Button>
        </Form.Item>
        <Spin spinning={fieldLoading} tip="校验中，请稍等！">
          <Form.Item
            label="字段名称"
            name="field"
            rules={[{ required: true }]}
            extra="请先通过授权校验后再选择字段名称"
          >
            <Select placeholder="请选择字段名称，如商品，选择商品id字段名">
              {primaryKeyOptions.map((item: any) => {
                return (
                  <Option value={item.value} key={item.value}>
                    <Tooltip
                      zIndex={10004}
                      autoAdjustOverflow
                      title={
                        <>
                          {item.label}
                          <br />
                          <span style={{ color: '#999999' }}>描述：{item.desc || '空'}</span>
                        </>
                      }
                    >
                      {item.label}
                      <br />
                      <span style={{ color: '#999999' }}>描述：{item.desc || '空'}</span>
                    </Tooltip>
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (value && value.includes('where')) {
                    return Promise.reject(new Error('仅需填写where之后的条件即可'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
            label="where条件"
            name="whereCondition"
          >
            <Input placeholder="请输入where条件" />
          </Form.Item>
          {(partitionOptions.length > 0 || odpsConfig?.partition) && (
            <Form.Item label="分区字段" name="partition" rules={[{ required: true }]}>
              <Select options={partitionOptions} placeholder="请选择ODPS表分区的字段名，如ds" />
            </Form.Item>
          )}
          <Form.Item wrapperCol={{ offset: 4 }}>
            <Button
              type="primary"
              onClick={() => {
                form.validateFields().then(() => {
                  Modal.confirm({
                    title: '确认上传',
                    content: '请仔细检查参数是否确认，上传成功后将不可二次修改!',
                    onOk: () => {
                      form.submit();
                    },
                  });
                });
              }}
              loading={submitLoading}
            >
              上传
            </Button>
          </Form.Item>
        </Spin>
      </Form>
    );
  };

  return (
    <>
      {global?.odpsResult && <Alert message={global?.odpsResult?.msg} style={{ marginBottom: 12 }} type="warning" />}
      {renderUploadForm()}
    </>
  );
};

export default UploadOdps;
