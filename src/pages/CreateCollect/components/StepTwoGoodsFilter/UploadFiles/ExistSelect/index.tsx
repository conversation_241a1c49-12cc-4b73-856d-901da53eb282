import { useState, useEffect, useRef } from 'react';
import { Select } from 'antd';
import { useGlobal } from '@ali/use-global';

import api from '@/api';

const { Option } = Select;

type OptionProps = {
  label: string;
  value: number;
  dataSource: string;
  activityOwner: string;
};

const ExistSelect = ({ value,onChange,acDataSource }: { value?: any;onChange:any,acDataSource:any }) => {
  const [searchText, setSearchText] = useState('');
  const [dataSource, setDataSource] = useState<OptionProps[]>([]);
  const timer = useRef(0);

  useEffect(() => {
    clearTimeout(timer.current);
    timer.current = setTimeout(() => {
      api.activity
        .list({
          pageNo: 1,
          pageSize: 50,
          isChild: 1,
          // parentId: 0,
          isOwner: true,
          query: searchText,
          dataSource: acDataSource
        })
        .then((res) => {
          let temp = res.data.map((e: Activity) => {
            return {
              ...e, // 其他信息还是保留，其他步骤要用
              label: e.activityTitle,
              value: e.id,
            };
          });
          // setDataSource(temp.filter((e) => e.parentId !== 0)); // 0表示是主活动，暂时先过滤掉
          setDataSource(temp)
        });
    }, 200) as unknown as number;
  }, [searchText,acDataSource]);

  const onClear = () => {
    setSearchText('')
  }

  return (
    <Select
      style={{ width: 300 }}
      onSearch={(val) => {
        setSearchText(val);
      }}
      onClear={onClear}
      allowClear={true}
      value={value}
      onChange={onChange}
      showSearch
      filterOption={false}
    >
      {dataSource.map((e) => (
        // option用来存放原始数据
        <Option key={e.value} value={e.value} option={e}>
          {e.label} {e.dataSource} {e.activityOwner}
        </Option>
      ))}
    </Select>
  );
};

export default ExistSelect;
