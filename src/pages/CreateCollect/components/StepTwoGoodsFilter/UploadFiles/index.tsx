import React, { useState } from 'react';
import { Button, Space, message, Upload, Popconfirm, Typography } from 'antd';
import { useGlobal } from '@ali/use-global';
import { getUrlParams } from '@/utils/utils';
import { DeleteOutlined } from '@ant-design/icons';

import UploadOdps from './UploadOdps';
import { downloadTemp } from '@/utils/download';

import api from '@/api';

const { Link } = Typography;

interface UploadFilesProps {
  uploadType: 'excel' | 'odps' | 'existCollection' | '';
  setLoading: (data: boolean) => void;
  existSelect: any;
}

const UploadFiles = (props: UploadFilesProps) => {
  const { uploadType, setLoading, existSelect,setIsUpload } = props;

  const [global, setGlobal] = useGlobal();
  const urlParams = getUrlParams();

  // 子活动id/品集id
  const collectionId = urlParams.collectionId;

  const [uploadFileList, setUploadFileList] = useState<any[]>([]);

  const uploadProps = {
    action: '/activity/UploadPic',
    name: 'pic',
    maxCount: 1,
    onChange(info: any) {
      setUploadFileList([...info.fileList]);

      if (info.file.status === 'done') {
        // 拿到上传的地址
        api.activity
          .createActivityByExcel({
            activityId: collectionId,
            excelUrl: info.file.response.data.data,
            operation: 'add',
          })
          .then((res) => {
            if (res.code === '200') {
              const temp = info.fileList.map((e) => {
                // 如果列表中的文件名跟刚才上传的文件名一致
                if (e.name === info.file.name) {
                  return {
                    ...e,
                    url: res.data,
                  };
                }
                return e;
              });
              setUploadFileList(temp);
              setIsUpload(false)
              // 上传完成后调用getActivity获取更新后的ruleGroup
              api.activity.getActivity({ id: collectionId, fetch: false }).then((res) => {
                if (res.code === '200' && res.msg === '操作成功') {
                  setGlobal({
                    activity: res.data.activity,
                  });
                }
              });
            } else {
              message.error(res.msg);
              api.activity.getActivity({ id: collectionId, fetch: false }).then((res) => {
                if (res.code === '200' && res.msg === '操作成功') {
                  setGlobal({
                    activity: res.data.activity,
                  });
                }
              });
              setUploadFileList([]);
            }
          });
      }

      if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件上传失败！`);
      }
    },
    fileList: uploadFileList,
    itemRender: (originNode: any, file: any) => {
      return (
        <a href={file.url || ''} download>
          {file.name}
        </a>
      );
    },
  };

  const createFlag = global?.activity?.createFlag;

  //删除表格
  const removeExcel = () => {
    api.activity
      .createActivityByExcel({
        activityId: collectionId,
        excelUrl: createFlag,
        operation: 'del',
      })
      .then((res) => {
        if (res.code === '200') {
          api.activity.getActivity({ id: collectionId, fetch: false }).then((res) => {
            if (res.code === '200' && res.msg === '操作成功') {
              setGlobal({
                activity: res.data.activity,
              });
            }
          });
          setUploadFileList([]);
        } else {
          message.error(res?.msg || '删除失败')
        }
      });
  };

  // excel上传后返回的链接参考：http://librawhitelist.oss-cn-hangzhou.aliyuncs.com/libra/XXXXXXX.xlsx
  const formattedExcelName = createFlag ? createFlag.split('/libra/')[1] : '';

  return (
    <div>
      <div style={{ display: uploadType === 'excel' ? 'block' : 'none' }}>
        {createFlag ? (
          <div>
            <a
              href={`/activity/DownloadExcel.do?excelUrl=${encodeURIComponent(createFlag)}&activityId=${collectionId}`}
              download
            >
              {formattedExcelName}
            </a>
            <Button
              type="link"
              icon={
                <Popconfirm title="是否要删除此表格" onConfirm={() => {removeExcel()}}>
                  <DeleteOutlined />
                </Popconfirm>
              }
            />
          </div>
        ) : (
          <Space>
            <Link onClick={downloadTemp}>模板</Link>
            ｜
            <Upload
              {...uploadProps}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: 240,
              }}
            >
              <Button style={{ padding: 0 }} type="link">
                上传表格
              </Button>
            </Upload>
          </Space>
        )}
      </div>
      <div style={{ display: uploadType === 'odps' ? 'block' : 'none' }}>
        <UploadOdps setLoading={setLoading} />
      </div>
      <div style={{ display: uploadType === 'existCollection' ? 'block' : 'none' }}>{existSelect}</div>
    </div>
  );
};

export default UploadFiles;
