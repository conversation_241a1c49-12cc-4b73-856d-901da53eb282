import React, { useState, useEffect, useRef } from 'react';
import { Button, Card, Space, Checkbox, List, message, Modal, Table, Row, Popover, Popconfirm, Tooltip } from 'antd';
import { useGlobal } from '@ali/use-global';
import { get, chunk } from 'lodash';

import { GoodsDataSourceTypeEnum, CollectStatusEnum } from '../../../constants';
import { PreviewTravelData } from '../../../interface';
import { CopyOutlined, DeleteOutlined } from '@ant-design/icons';

import { updateHash, getUrlParams, copyToClipboard, filterCheck, filterCheckName, getGLBType } from '@/utils/utils';

import api from '@/api';

import styles from './index.less';
import SearchForm from './searchForm';
import SortBtn from './sortBtn';
import PreviewRenderItem from './previewRenderItem';
import { tripTypeMap } from './common';
import dayjs from 'dayjs';

interface PreviewProps {
  dataSource: GoodsDataSourceTypeEnum;
  rules: any[]; // 新的规则组列表
  hasTag: number; // 是否打标，0表示不打标，1表示打标
  uploadType: 'excel' | 'odps' | 'existCollection' | ''; //  上传类型，两种
  /** 是否是正在进行选品中，StepTwoGoodsFilter 中用到了这个 */
  isCreatingCollect?: boolean;
  // 第一次是否自动请求选品池数据
  autoFirstFetch?: boolean;
  limitCount?: number; // 选品数量
}

const generateItemUrl = (itemId: number) => `https://items.fliggy.com/item.htm?id=${itemId}`;

const Preview = (props: PreviewProps) => {
  const init = useRef(true);
  const { dataSource, rules, hasTag, uploadType, isCreatingCollect, autoFirstFetch, limitCount, isUpload } = props;
  const [selectedItems, setSelectedItems] = useState<number[] | string[]>([]);
  const [searchText, setSearchText] = useState('');
  const [previewList, setPreviewList] = useState<PreviewTravelData[]>([]); // 预览列表数据
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [sortType, setSortType] = useState('');
  const [isCheck, setIsCheck] = useState(false);

  const [loading, setLoading] = useState(false);
  const [updateTag, setUpdateTag] = useState(false);
  const [showSize, setShowSize] = useState(0);

  const [filterOff, setFilterOff] = useState<any[]>([]); //过滤商品下架
  const [filterOver, setFilterOver] = useState<any[]>([]); //过滤商品过期
  const [filterNon, setFilterNon] = useState<any[]>([]); //过滤商家不存在
  const [filterTest, setFilterTest] = useState<any[]>([]); //过滤商品测试
  const [filterNoPrice, setFilterNoPrice] = useState<any[]>([]); //商品/酒店无价格 - 自查一下是不是未来可售的酒店
  const [filterBlack, setFilterBlack] = useState<any[]>([]); //商品被剔除在黑名单里
  const [exceptOff, setExceptOff] = useState<any[]>([]); //已除去商品

  const [exceptLoading, setExceptLoading] = useState<boolean>(false);
  const [isShow, setIsShow] = useState<boolean>(false);

  const [global, setGlobal] = useGlobal();

  const params = getUrlParams();

  useEffect(() => {
    if (updateTag) {
      setLoading(true);
      fetchPreview(sortType, pageNo);
      firstBlackList();
      if (params.type === '2' || params.type === '3') {
        if (uploadType !== '' && uploadType !== 'existCollection') {
          uploadItemFilterCheck();
        }
      }
    }
  }, [updateTag]);

  // 默认进入的时候就请求一次选品池数据
  useEffect(() => {
    if (autoFirstFetch && isUpload && rules.length > 0) {
      setLoading(true);
      setTimeout(() => {
        fetchPreview(sortType, pageNo);
      }, 1000);

      firstBlackList();
      if (params.type === '2' || params.type === '3') {
        if (uploadType !== '' && uploadType !== 'existCollection') {
          uploadItemFilterCheck();
        }
      }
    }
  }, [autoFirstFetch, global?.activity]);

  // 删除execl重新请求
  useEffect(() => {
    if (rules.length === 0) {
      setPreviewList([]);
      setTotal(0);
      setShowSize(0);
      setFilterOff([]);
      setFilterOver([]);
      setFilterTest([]);
      setExceptOff([]);
      setFilterNoPrice([]);
      setFilterBlack([]);
      setFilterNon([]);
    }
  }, [rules]);

  const previewFetchCallback = (res: any) => {
    setUpdateTag(false);
    if (res.code === '200' && res.msg === '操作成功') {
      setLoading(false);
      setPreviewList(get(res, 'data.pageResult.data') || []);
      setTotal(res.data.pageResult.total);
      setShowSize(res.data.previewCnt ? res.data.previewCnt : res.data.pageResult.total);
    } else {
      setLoading(false);
      setPreviewList([]);
      setShowSize(0);
      setTotal(0);
      if (res.code === '501') {
        setGlobal({
          odpsResult: res,
        });
      } else {
        message.warning(res.msg);
      }
    }
  };

  const fetchPreview = (sortTypes: any, pageNo: any) => {
    let temp: any;

    if (sortTypes === 'tagSortPre') {
      temp = {
        id: params.collectionId,
        ruleGroup: JSON.stringify(rules),
        pageNo,
        pageSize,
        extQuery: JSON.stringify({
          query: searchText,
          sort: 'relScore',
          sortType: 'asc',
        }),
      };
    } else {
      temp = {
        id: params.collectionId,
        ruleGroup: JSON.stringify(rules),
        pageNo,
        pageSize,
        extQuery: JSON.stringify({
          query: searchText,
          sort: sortTypes,
        }),
      };
    }

    if (limitCount !== undefined) {
      temp.limitCount = limitCount;
    }
    setLoading(true);
    api.activity.preview(temp).then(previewFetchCallback);
  };

  const confirmDialog = (func: any) => {
    const modal = Modal.confirm({
      title: '提示',
      content: '该操作无法撤销，确定继续吗？',
      onOk: () => {
        func();
      },
      onCancel: () => {
        modal.destroy();
      },
    });
  };

  const removeGoods = (itemIds: number[]) => {
    // console.log('去除货品', itemIds);
    setLoading(true);
    api.activity
      .saveBlack({
        activityId: params.collectionId,
        itemIds,
      })
      .then((res) => {
        // console.log('res', res);
        setLoading(false);
        if (res.code === '200' && res.msg === '操作成功') {
          fetchPreview(sortType, pageNo);
          firstBlackList();
          setIsCheck(false);
        }
      });
  };

  // const exportSelectedGoods = () => {
  //   console.log('导出货品', selectedItems);
  // };

  // const exportAllGoods = () => {
  //   console.log('导出货品', previewList);
  // };

  const renderCheckBox = (itemId: number | string, styles?: any) => {
    return (
      <Checkbox
        onChange={(e) => {
          if (e.target.checked) {
            setSelectedItems([...selectedItems, itemId]);
          } else {
            setSelectedItems(selectedItems.filter((e) => e !== itemId));
          }
        }}
        checked={selectedItems.includes(itemId)}
        style={{ position: 'absolute', zIndex: 100, left: '20px', top: '15px', ...(styles || {}) }}
      />
    );
  };

  const jumpToNextStep = (step: CollectStatusEnum) => {
    const urlParams = getUrlParams();
    // 跳转到下一步
    updateHash({
      params: {
        type: urlParams.type, // 选品类型FilterType
        step,
        collectionId: urlParams.collectionId, // 创建成功的品集id
        isFrame: window.location.href.includes('isFrame=true') ? true : false,
      },
    });
  };

  /**
   * 进入打标页面
   * @param step
   * @param itemCount 商品总数
   */
  const jumpToGoodsMarking = (itemCount: number) => {
    const urlParams = getUrlParams();
    // 跳转到下一步

    // 将itemCount作为临时数据传入下一步
    updateHash({
      params: {
        type: urlParams.type, // 选品类型FilterType
        step: CollectStatusEnum.MAKETAGS,
        collectionId: urlParams.collectionId, // 创建成功的品集id
        isFrame: window.location.href.includes('isFrame=true') ? true : false,
      },
    });
    setGlobal({
      itemCount,
    });
  };

  const publishCollection = async () => {
    // 如果是odps，不需要校验这个流程
    if (!uploadType && rules.length === 0) {
      message.error('请至少添加一个规则之后再进行发布');
      return;
    }

    // 预览数据为0时，不能发布
    // if (previewList.length === 0) {
    //   message.error('选品结果为0不能发布选品集');
    //   return;
    // }

    const temp: any = {
      id: params.collectionId,
      ruleGroup: JSON.stringify(rules),
    };

    if (limitCount) {
      temp.limitCount = limitCount;
    }
    // 如果需不要打标，则保存规则之后直接发布，然后进入下一步
    if (hasTag === 0) {
      setLoading(true);
      const res1 = await api.activity.saveActivityRule(temp);
      if (res1.code === '200' && res1.msg === '操作成功') {
        // 如果没有打标的步骤，则不传tags字段
        const res2 = await api.activity.publish({
          id: params.collectionId,
          itemCount: res1.data.total,
        });
        if (res2.code === '200' && res2.msg === '操作成功') {
          setLoading(false);
          jumpToNextStep(CollectStatusEnum.PUBLISHCOLLECT);
        } else {
          setLoading(false);
          message.error(res2.msg);
        }
      } else {
        setLoading(false);
        message.error(res1.msg);
      }
    } else {
      // 如果需要打标(hasTag === 1)
      const res = await api.activity.saveActivityRule(temp);
      if (res.code === '200' && res.msg === '操作成功') {
        // 获取活动信息，检查是否已经在创建选品池时选择了IC标
        const activityRes = await api.activity.getActivity({
          id: params.collectionId,
          fetch: false,
        });

        if (activityRes.code === '200' && activityRes.msg === '操作成功' && activityRes.data.activity.tags) {
          // 如果已经选择了IC标，直接发布
          const res2 = await api.activity.publish({
            id: params.collectionId,
            itemCount: res.data.total,
            tags: activityRes.data.activity.tags,
          });

          if (res2.code === '200' && res2.msg === '操作成功') {
            setLoading(false);
            jumpToNextStep(CollectStatusEnum.PUBLISHCOLLECT);
          } else {
            setLoading(false);
            message.error(res2.msg);
          }
        } else {
          // 如果没有选择IC标，跳转到打标页面
          jumpToGoodsMarking(res.data.total);
        }
      } else {
        setLoading(false);
        message.error(res.msg);
      }
    }
  };

  // 已除去商品弹窗
  const contentExcept = (dataSource: any, type: number) => {
    const toCopy = (dataSource: any) => {
      copyToClipboard(dataSource.map((e) => e.itemID).join('\n'));
      message.success('复制成功');
    };

    const confirmAll = () => {
      api.activity
        .removeBlack({ activityId: params.collectionId, itemIds: dataSource.map((e) => e.itemID).join() })
        .then((res: any) => {
          if (res.code === '200') {
            message.success(res.msg);
            blackList();
            fetchPreview(sortType, pageNo);
          } else {
            message.error(res.msg);
          }
        });
    };

    const confirm = (record: any) => {
      api.activity.removeBlack({ activityId: params.collectionId, itemIds: record.itemID }).then((res: any) => {
        if (res.code === '200') {
          message.success(res.msg);
          blackList();
          fetchPreview(sortType, pageNo);
        } else {
          message.error(res.msg);
        }
      });
    };

    const columns: any = [
      {
        title: () => {
          return (
            <>
              <span>{`${filterCheckName(props.dataSource)}ID`}</span>
              <Button
                type="link"
                disabled={!(dataSource.length > 0)}
                onClick={() => {
                  toCopy(dataSource);
                }}
              >
                <CopyOutlined />
              </Button>
            </>
          );
        },
        dataIndex: 'itemID',
        key: 'itemID',
        width: 200,
      },
      {
        title: () => {
          return (
            <>
              <span>操作</span>
              <Popconfirm title="确认删除全部已去除的商品？" onConfirm={confirmAll} disabled={!(dataSource.length > 0)}>
                <Button type="link" disabled={!(dataSource.length > 0)}>
                  <DeleteOutlined />
                </Button>
              </Popconfirm>
            </>
          );
        },
        dataIndex: 'handle',
        align: 'center',
        width: 200,
        render: (text: any, record: any, index: number) => {
          return (
            <Popconfirm
              title="确认删除已去除的商品？"
              onConfirm={() => {
                confirm(record);
              }}
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          );
        },
      },
    ];
    return (
      <>
        <Table
          loading={exceptLoading}
          rowKey={(record) => record.itemID}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          scroll={{ y: 240 }}
          className={styles.preview_table}
        />
        <div style={{ color: 'red' }}>注：删除商品后商品可正常被选出</div>
      </>
    );
  };

  // 过滤商品
  const contentFilter = (dataSource: any, type: number) => {
    const toCopy = (dataSource: any) => {
      copyToClipboard(dataSource.map((e) => e.itemId).join('\n'));
      message.success('复制成功');
    };

    const columns: any = [
      {
        title: () => {
          return (
            <>
              <span>{`${filterCheckName(props.dataSource)}ID`}</span>
              <Button
                type="link"
                disabled={!(dataSource.length > 0)}
                onClick={() => {
                  toCopy(dataSource);
                }}
              >
                <CopyOutlined />
              </Button>
            </>
          );
        },
        dataIndex: 'itemId',
        key: 'itemId',
        width: 200,
      },
      {
        title: '过滤原因',
        dataIndex: 'reasonEnum',
        key: 'reasonEnum',
        align: 'center',
        width: 200,
        render: (text: any, record: any, index: number) => {
          return <div>{filterCheck(text)}</div>;
        },
      },
    ];
    return (
      <Table
        rowKey={(record) => record.itemId}
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        scroll={{ y: 240 }}
        className={styles.preview_table}
      />
    );
  };

  const blackList = () => {
    setExceptLoading(true);
    api.activity.blackList({ activityId: params.collectionId }).then((res: any) => {
      if (res.code === '200') {
        if (res.data && res.data.length > 0) {
          const temp = res.data.map((ele) => {
            return { itemID: ele };
          });
          setExceptOff(temp);
          setExceptLoading(false);
        } else {
          setExceptLoading(false);
          setExceptOff([]);
        }
      } else {
        message.error(res.msg);
        setExceptLoading(false);
        setExceptOff([]);
      }
    });
  };

  // 第一次请求
  const firstBlackList = () => {
    setExceptLoading(true);
    api.activity.blackList({ activityId: params.collectionId }).then((res: any) => {
      if (res.code === '200') {
        if (res.data && res.data.length > 0) {
          const temp = res.data.map((ele) => {
            return { itemID: ele };
          });
          setExceptOff(temp);
          setExceptLoading(false);
          setIsShow(true);
        } else {
          setExceptLoading(false);
          setIsShow(false);
        }
      } else {
        message.error(res.msg);
        setExceptLoading(false);
        setIsShow(false);
      }
    });
  };

  const uploadItemFilterCheck = () => {
    api.activity.uploadItemFilterCheck({ activityId: params.collectionId }).then((res: any) => {
      if (res.code === '200') {
        if (res.data.OFF_SHELF && res.data.OFF_SHELF.length > 0) {
          setFilterOff(res.data.OFF_SHELF);
        }

        if (res.data.EXPIRED && res.data.EXPIRED.length > 0) {
          setFilterOver(res.data.EXPIRED);
        }

        if (res.data.TEST && res.data.TEST.length > 0) {
          setFilterTest(res.data.TEST);
        }

        if (res.data.NO_ITEM && res.data.NO_ITEM.length > 0) {
          setFilterNon(res.data.NO_ITEM);
        }

        if (res.data.NO_PRICE && res.data.NO_PRICE.length > 0) {
          setFilterNoPrice(res.data.NO_PRICE);
        }

        if (res.data.BLACK && res.data.BLACK.length > 0) {
          setFilterBlack(res.data.BLACK);
        }
      } else {
        message.error(res.msg);
      }
    });
  };

  const pagination = {
    total: dataSource !== 'FLIGHT' ? total : previewList?.length || 0,
    pageSize,
    pageNo,
    showSizeChanger: false,
    onChange: (newPage: any) => {
      setPageNo(newPage);
      if (dataSource !== 'FLIGHT') {
        setUpdateTag(true);
      } else {
        setIsCheck(false);
      }
    },
  };

  const getPreview = () => {
    setUpdateTag(true);
  };

  return (
    <div className={styles.filter_result}>
      {isCreatingCollect && (
        <div className={styles.check_result_btn_box}>
          <Button
            type="primary"
            onClick={() => {
              getPreview();
            }}
          >
            查看结果
          </Button>
        </div>
      )}
      <Card title="选品结果" bordered={false}>
        <div className={styles.row}>
          <div>
            筛选出{showSize}个{filterCheckName(dataSource)}
          </div>
          <SearchForm
            value={searchText}
            onChange={(val: any) => {
              setSearchText(val);
            }}
            dataSource={dataSource}
            onSearch={() => {
              setPageNo(1);
              setSortType('');
              fetchPreview('', 1);
            }}
          />
        </div>
        <div style={{ width: '46%' }}>
          {params.type === '2' &&
            (filterOff.length > 0 ||
              filterNon.length > 0 ||
              filterOver.length > 0 ||
              filterNoPrice.length > 0 ||
              filterBlack.length > 0 ||
              filterTest.length > 0) && (
              <Row className={styles.row_center}>
                <div style={{ whiteSpace: 'nowrap' }}>{`过滤${filterCheckName(dataSource)}：`}</div>
                {filterOff.length > 0 && (
                  <div>
                    <span>下架</span>
                    <Popover content={contentFilter(filterOff, 1)} trigger="click">
                      <Button type="link">{filterOff.length}</Button>
                    </Popover>
                  </div>
                )}
                {filterOver.length > 0 && (
                  <div>
                    <span>过期</span>
                    <Popover content={contentFilter(filterOver, 2)} trigger="click">
                      <Button type="link">{filterOver.length}</Button>
                    </Popover>
                  </div>
                )}
                {filterNon.length > 0 && (
                  <div>
                    <span>不存在</span>
                    <Popover content={contentFilter(filterNon, 2)} trigger="click">
                      <Button type="link">{filterNon.length}</Button>
                    </Popover>
                  </div>
                )}
                {filterTest.length > 0 && (
                  <div>
                    <span>测试</span>
                    <Popover content={contentFilter(filterTest, 3)} trigger="click">
                      <Button type="link">{filterTest.length}</Button>
                    </Popover>
                  </div>
                )}
                {filterNoPrice.length > 0 && (
                  <div>
                    <span>商品/酒店无价格 - 自查一下是不是未来可售的酒店</span>
                    <Popover content={contentFilter(filterNoPrice, 3)} trigger="click">
                      <Button type="link">{filterNoPrice.length}</Button>
                    </Popover>
                  </div>
                )}
                {filterBlack.length > 0 && (
                  <div>
                    <span>商品被剔除在黑名单里</span>
                    <Popover content={contentFilter(filterBlack, 3)} trigger="click">
                      <Button type="link">{filterBlack.length}</Button>
                    </Popover>
                  </div>
                )}
              </Row>
            )}
          {isShow && (
            <Row className={styles.row_center}>
              <div style={{ whiteSpace: 'nowrap' }}>
                <span>{`人工去除${filterCheckName(props.dataSource)}：`}</span>
              </div>
              <div>
                <Popover content={contentExcept(exceptOff, 1)} trigger="click">
                  <Button type="link">{exceptOff.length}</Button>
                </Popover>
              </div>
            </Row>
          )}
        </div>
        <div
          style={{
            marginBottom: 10,
            borderBottom: '1px solid #9d9d9d',
            display: 'flex',
            justifyContent: 'space-between',
            marginTop: 20,
          }}
        >
          <Space style={{ paddingBottom: 10 }}>
            <Checkbox
              checked={isCheck}
              onChange={(e) => {
                setIsCheck(e.target.checked);
                if (e.target.checked) {
                  if (dataSource === 'GLB') {
                    // 哥伦布要特殊处理取stringTypeItemId
                    setSelectedItems(previewList.map((e) => e.stringTypeItemId));
                  } else if (dataSource === 'FLIGHT') {
                    if (previewList?.length > 0) {
                      // 将list拆分成多个 pageSize 长度的区块,通过pageNo作为索引找到当前页数据
                      const pageData = chunk(previewList, pageSize || 50);
                      const currentPageData = pageData[pageNo - 1] || [];
                      setSelectedItems(currentPageData.map((e) => e.id));
                    } else {
                      setSelectedItems([]);
                    }
                  } else {
                    setSelectedItems(previewList.map((e) => e.itemId));
                  }
                } else {
                  setSelectedItems([]);
                }
              }}
            >
              选中本页
            </Checkbox>
            <Button
              onClick={() => {
                confirmDialog(() => {
                  removeGoods(selectedItems);
                });
              }}
            >
              去除货品
            </Button>
            {/* 导出的这期先不做 */}
            {/* <Button onClick={exportSelectedGoods}>导出选中货品</Button> */}
            {/* <Button onClick={exportAllGoods}>导出全部</Button> */}
          </Space>
          <SortBtn
            fetchPreview={fetchPreview}
            setSortType={setSortType}
            setPageNo={setPageNo}
            dataSource={dataSource}
          />
        </div>
        <List
          loading={loading}
          grid={{
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 3,
            lg: 3,
            xl: 4,
            xxl: 5,
          }}
          dataSource={previewList}
          renderItem={(item) => (
            <PreviewRenderItem
              items={item}
              dataSource={dataSource}
              renderCheckBox={renderCheckBox}
              generateItemUrl={generateItemUrl}
              confirmDialog={confirmDialog}
              removeGoods={removeGoods}
              GoodsDataSourceTypeEnum={GoodsDataSourceTypeEnum}
              tripTypeMap={tripTypeMap}
              getGLBType={getGLBType}
              type="preview"
            />
          )}
          style={{ minHeight: 250, marginTop: 20 }}
          pagination={total > pageSize ? pagination : false}
        />
      </Card>
      {isCreatingCollect && (
        <div className={styles.result_btn}>
          <Tooltip
            title={
              global && global.activity && dayjs().valueOf() > dayjs(global.activity.expireTime).valueOf()
                ? '选品池已过期'
                : ''
            }
          >
            <Button
              disabled={global && global.activity && dayjs().valueOf() > dayjs(global.activity.expireTime).valueOf()}
              onClick={publishCollection}
            >
              发布选品集
            </Button>
          </Tooltip>
        </div>
      )}
    </div>
  );
};

export default Preview;
