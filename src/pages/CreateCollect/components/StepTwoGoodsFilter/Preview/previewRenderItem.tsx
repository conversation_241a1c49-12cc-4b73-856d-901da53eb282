import React from 'react';
import { Card, List, Modal, Table } from 'antd';
import {
  PreviewTravelData,
  PreviewHotelData,
  PreviewPOIData,
  PreviewLPData,
  PreviewRPData,
  PreviewShopData,
  PreviewHomeStayData,
  PreviewGLBStayData,
  PreviewFLIGHTStayData,
  PreviewSPUStayData
} from '@/pages/CreateCollect/interface';
import styles from './index.less';

const PreviewRenderItem = (props: any) => {
  const {
    dataSource,
    renderCheckBox,
    generateItemUrl,
    confirmDialog,
    removeGoods,
    GoodsDataSourceTypeEnum,
    getGLBType,
    items,
    tripTypeMap,
    type,
  } = props;

  const renderTravelItem = (item: PreviewTravelData) => {
    return (
      <List.Item>
        <Card style={{ padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.itemId)}
          <a href={generateItemUrl(item.itemId)} target="_blank">
            <div
              style={{
                backgroundImage: `url(${item.headPic})`,
                height: '220px',
                backgroundSize: 'cover',
              }}
            />
          </a>
          <div style={{ fontSize: '13px' }}>
            <a href={generateItemUrl(item.itemId)} target="_blank">
              <h3 style={{ margin: '10px 0 10px 0' }}>{item.name}</h3>
            </a>
            <div>
              <p>
                价格：
                <span style={{ color: 'red' }}>
                  ¥{item.discountPrice / 100}(促) <small style={{ fontSize: '10px' }}> 起</small>
                </span>
                <span style={{ textDecoration: 'line-through' }}>¥{item.reservePrice / 100}(原)</span>
              </p>
              <p>卖家昵称： {item.sellerNick}</p>
              <p>类目： {item.category}</p>
              <p>30天付款人数： {item.saleCount}</p>
              {item.tagInfos &&
                item.tagInfos.map((tagInfo) => (
                  <p key={tagInfo.tagName}>
                    {tagInfo.tagName} &nbsp; {tagInfo.relScore}
                  </p>
                ))}
            </div>
            {type === 'preview' && (
              <p
                onClick={() => {
                  confirmDialog(() => {
                    removeGoods([item.itemId]);
                  });
                }}
                style={{ textAlign: 'right', cursor: 'pointer', color: 'blue' }}
              >
                去除该商品
              </p>
            )}
          </div>
        </Card>
      </List.Item>
    );
  };

  /**商家 */
  const renderShopItem = (item: PreviewShopData) => {
    return (
      <List.Item>
        <Card style={{ padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.itemId)}
          <div style={{ fontSize: '13px', marginTop: '40px' }}>
            <a href={generateItemUrl(item.itemId)} target="_blank">
              <h3 style={{ margin: '10px 0 10px 0' }}>{item.name}</h3>
            </a>
            <div>
              <p>
                天猫商家状态：
                <span>{item.shopStatus}</span>
              </p>
              <p>商家经营范围： {item.shopScope}</p>
              <p>商家性质： {item.shopType}</p>
            </div>
            {type === 'preview' && (
              <p
                onClick={() => {
                  confirmDialog(() => {
                    removeGoods([item.itemId]);
                  });
                }}
                style={{ textAlign: 'right', cursor: 'pointer', color: 'blue' }}
              >
                去除该商品
              </p>
            )}
          </div>
        </Card>
      </List.Item>
    );
  };

  /** 老逻辑酒店不能勾选 现在能勾选*/
  const renderHotelItem = (item: PreviewHotelData) => {
    return (
      <List.Item>
        <Card style={{ padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.itemId)}
          <a href={`https://hotel.fliggy.com/hotel_detail2.htm?shid=${item.itemId}`} target="_blank">
            <div
              style={{ backgroundImage: `url(${item.headPic})`, height: '220px', backgroundSize: 'cover' }}
            />
          </a>
          <div style={{ fontSize: '13px' }}>
            <a href={`https://hotel.fliggy.com/hotel_detail2.htm?shid=${item.itemId}`} target="_blank">
              <h3 style={{ margin: '10px 0 10px 0' }}>
                {item.name}
                {item.supportCredit === '1' && (
                  <span
                    style={{
                      display: 'inline',
                      margin: '0 5px',
                      padding: '2px 24px 0',
                      background:
                        'transparent url(//img.alicdn.com/tps/i3/TB1Z8s1HFXXXXX7XVXXJ_op3XXX-114-525.png) 0 -177px no-repeat',
                    }}
                  />
                )}
              </h3>
            </a>
            {dataSource && dataSource === 'HOTEL' && (
              <div>
                <p>
                  国家/城市：{item.hotelCountry}/{item.hotelCity}
                </p>
                <p>评分：{item.hotelRateAvg}</p>
                <p>评价数：{item.hotelRateNum}</p>
                <p>参考价格：{item.hotelPrice}</p>
                <p>30天成交间夜数： {item.hotelCheckinQty1m}</p>
                <p>卖家30天成交间夜数 ：{item.selectedHotelCheckinQty1m}</p>
                <p>
                  卖家： {item.sellerDesc} &nbsp;&nbsp;
                  <span
                    style={{ color: 'blue', textDecoration: 'underline', cursor: 'pointer' }}
                    onClick={() => {
                      const modal = Modal.info({
                        title: '买家列表',
                        width: 1000,
                        content: (
                          <Table
                            dataSource={item.sellerInfoList}
                            columns={[
                              {
                                title: '卖家名称',
                                dataIndex: 'sellerName',
                                key: 'sellerName',
                              },
                              {
                                title: '参考价',
                                dataIndex: 'price',
                                key: 'price',
                              },
                              {
                                title: '近30天成交间夜量',
                                dataIndex: 'saleCountRecent',
                                key: 'saleCountRecent',
                              },
                              {
                                title: '是否支持信用住',
                                key: 'creditPay',
                                render: (text, record) => {
                                  return record.creditPay === '1' ? '支持' : '不支持';
                                },
                              },
                            ]}
                          />
                        ),
                        onCancel: () => {
                          modal.destroy();
                        },
                      });
                    }}
                  >
                    查看卖家列表
                  </span>
                </p>
              </div>
            )}
          </div>
        </Card>
      </List.Item>
    );
  };

  const renderPOIItem = (item: PreviewPOIData) => {
    return (
      <List.Item>
        <Card style={{ padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.itemId)}
          <a href={generateItemUrl(item.itemId)} target="_blank">
            <div
              style={{ backgroundImage: `url(${item.bigImage})`, height: '220px', backgroundSize: 'cover' }}
            />
          </a>
          <div style={{ fontSize: '13px' }}>
            <div>
              {item.labelList
                ? item.labelList.map((e, index) => {
                    if (index === 0) {
                      return (
                        <a href={generateItemUrl(item.itemId)} target="_blank">
                          <h3 style={{ margin: '10px 0 10px 0' }}>{e}</h3>
                        </a>
                      );
                    }
                    return <p>{e}</p>;
                  })
                : null}
            </div>
            {type === 'preview' && (
              <p
                onClick={() => {
                  const modal = Modal.confirm({
                    title: '提示',
                    content: '该操作无法撤销，确定继续吗？',
                    onOk: () => {
                      confirmDialog(() => {
                        removeGoods([item.itemId]);
                      });
                    },
                    onCancel: () => {
                      modal.destroy();
                    },
                  });
                }}
                style={{ textAlign: 'right', cursor: 'pointer', color: 'blue' }}
              >
                {item.deleteText}
              </p>
            )}
          </div>
        </Card>
      </List.Item>
    );
  };

  const renderLPItem = (item: PreviewLPData) => {
    return (
      <List.Item>
        {type === 'preview' && renderCheckBox(item.itemId)}
        <Card style={{ padding: '10px' }} bodyStyle={{ padding: 0 }}>
          <a href={generateItemUrl(item.itemId)} target="_blank">
            <div
              style={{ backgroundImage: `url(${item.headPic})`, height: '220px', backgroundSize: 'cover' }}
            />
          </a>
          <div style={{ fontSize: '13px' }}>
            <a href={generateItemUrl(item.itemId)} target="_blank">
              <h3 style={{ margin: '10px 0 10px 0' }}>{item.name}</h3>
            </a>
            {type === 'preview' && (
              <p
                onClick={() => {
                  const modal = Modal.confirm({
                    title: '提示',
                    content: '该操作无法撤销，确定继续吗？',
                    onOk: () => {
                      confirmDialog(() => {
                        removeGoods([item.itemId]);
                      });
                    },
                    onCancel: () => {
                      modal.destroy();
                    },
                  });
                }}
                style={{ textAlign: 'right', cursor: 'pointer', color: 'blue' }}
              >
                去除该商品
              </p>
            )}
          </div>
        </Card>
      </List.Item>
    );
  };

  /** RP也不能勾选？ */
  const renderRPItem = (item: PreviewRPData) => {
    return (
      <List.Item>
        <Card style={{ padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.itemId)}
          <a href={generateItemUrl(item.itemId)} target="_blank">
            <div
              style={{ backgroundImage: `url(${item.headPic})`, height: '220px', backgroundSize: 'cover' }}
            />
          </a>
          <div style={{ fontSize: '13px' }}>
            <div>
              <a target="_blank">
                <h3 style={{ margin: '10px 0 10px 0' }}>{item.name}</h3>
              </a>
            </div>
            <div>
              <p>标准酒店名称：{item.shotelName}</p>
              <p>SHID：{item.shid}</p>
              <p>RP名称：{item.name}</p>
              <p>RPID：{item.itemId}</p>
            </div>
          </div>
        </Card>
      </List.Item>
    );
  };

  /** 民宿*/
  const renderHomeStayItem = (item: PreviewHomeStayData) => {
    return (
      <List.Item>
        <Card style={{  padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.itemId)}
          <a href={item.url} target="_blank">
            <div
              style={{ backgroundImage: `url(${item.headPic})`, height: '220px', backgroundSize: 'cover' }}
            />
          </a>
          <div style={{ fontSize: '13px' }}>
            <a href={item.url} target="_blank">
              <h3 style={{ margin: '10px 0 10px 0' }}>{item.name}</h3>
            </a>
            {dataSource && dataSource === 'BNB' && (
              <div>
                <p>民宿价格：{item.reservePrice}</p>
                <p>卖家名字：{item.sellerNick}</p>
                <p>民宿评分：{item.score}</p>
                <p>未来30天可售天数 ：{item.bnbKsNum}</p>
              </div>
            )}
          </div>
        </Card>
      </List.Item>
    );
  };

  /** 哥伦布榜单*/
  const renderGLBStayItem = (item: PreviewGLBStayData) => {
    return (
      <List.Item>
        <Card style={{ padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.stringTypeItemId)}
          <a href={item.url} target="_blank">
            <div
              style={{ backgroundImage: `url(${item.headPic})`, height: '220px', backgroundSize: 'cover' }}
            />
          </a>
          <div style={{ fontSize: '13px' }}>
            <a href={item.url} target="_blank">
              <h3 style={{ margin: '10px 0 10px 0' }}>{item.name}</h3>
            </a>
            {dataSource && dataSource === 'GLB' && (
              <div>
                <p>目的地：{item.dest}</p>
                <p>榜单ID：{item.stringTypeItemId}</p>
                <p>榜单类型：{getGLBType(item.type)}</p>
                <p>商品数量：{item.count}</p>
              </div>
            )}
          </div>
        </Card>
      </List.Item>
    );
  };

  /** 机票*/
  const renderFLIGHTCardList = (item: PreviewFLIGHTStayData) => {
    return (
      <List.Item>
        <Card style={{ padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.id, { left: '5px', top: '4px' })}
          <div className={styles.flightsCardTitle}>{`${item.depCityName}-${item.arrCityName}`}</div>
          <p>
            时间：
            <span>
              {item.depTime}至{item.arrTime}
            </span>
          </p>
          <p>
            航班：<span>{item.flightNo}</span>
          </p>
          <p>
            出发机场：<span>{item.depAirportName}</span>
          </p>
          <p>
            到达机场：<span>{item.arrAirportName}</span>
          </p>
          <p>
            航程类型：<span>{tripTypeMap[item.tripType]}</span>
          </p>
          <p>
            是否直飞：<span>{item.direct ? '是' : '否'}</span>
          </p>
          <p>
            价格：
            <span style={{ color: 'red' }}>
              ¥{item.sellPrice || 0}
              <small style={{ fontSize: '10px' }}> 起</small>
            </span>
          </p>
          {item.bizType === 1 ? null : (
            <p>
              折扣：
              {item.discountInfo ? (
                <span>
                  {item.discountInfo}
                  <small style={{ fontSize: '10px' }}> 起</small>
                </span>
              ) : (
                ''
              )}
            </p>
          )}
        </Card>
      </List.Item>
    );
  };


  /** SPU*/
  const renderSPUStayItem = (item: PreviewSPUStayData) => {
    return (
      <List.Item>
        <Card style={{  padding: '10px' }} bodyStyle={{ padding: 0 }}>
          {type === 'preview' && renderCheckBox(item.itemId)}
          <div
            style={{ backgroundImage: `url(${item.headPic})`, height: '220px', backgroundSize: 'cover' }}
          />
          <div style={{ fontSize: '13px' }}>
            <h3 style={{ margin: '10px 0 10px 0' }}>{item.spuTitle}</h3>
            {dataSource && dataSource === 'SPU' && (
              <div>
                <p>商品数量：{item.itemNum}</p>
                <p>标签：{item.spuLabels}</p>
                <p>包含POI：{item.containPoi}</p>
                <p>包含城市：{item.containCity}</p>
                <p>SPU天数：{item.spuDays}</p>
              </div>
            )}
          </div>
        </Card>
      </List.Item>
    );
  };

  const renderItem = (
    item:
      | PreviewTravelData
      | PreviewHotelData
      | PreviewPOIData
      | PreviewLPData
      | PreviewRPData
      | PreviewShopData
      | PreviewHomeStayData
      | PreviewGLBStayData
      | PreviewFLIGHTStayData
      | PreviewSPUStayData
  ) => {
    if (dataSource === GoodsDataSourceTypeEnum.TRAVEL) {
      return renderTravelItem(item as PreviewTravelData);
    }
    if (dataSource === GoodsDataSourceTypeEnum.HOTEL) {
      return renderHotelItem(item as PreviewHotelData);
    }
    if (dataSource === GoodsDataSourceTypeEnum.POI) {
      return renderPOIItem(item as PreviewPOIData);
    }
    if (dataSource === GoodsDataSourceTypeEnum.LP) {
      return renderLPItem(item as PreviewLPData);
    }
    if (dataSource === GoodsDataSourceTypeEnum.RP) {
      return renderRPItem(item as PreviewRPData);
    }
    if (dataSource === GoodsDataSourceTypeEnum.SHOP) {
      return renderShopItem(item as PreviewShopData);
    }

    if (dataSource === GoodsDataSourceTypeEnum.BNB) {
      return renderHomeStayItem(item as PreviewHomeStayData);
    }

    if (dataSource === GoodsDataSourceTypeEnum.GLB) {
      return renderGLBStayItem(item as PreviewGLBStayData);
    }

    if (dataSource === GoodsDataSourceTypeEnum.FLIGHT) {
      return renderFLIGHTCardList(item as PreviewFLIGHTStayData);
    }

    if (dataSource === GoodsDataSourceTypeEnum.SPU) {
      return renderSPUStayItem(item as PreviewSPUStayData);
    }

    return <List.Item></List.Item>;
  };

  return renderItem(items);
};

export default PreviewRenderItem;
