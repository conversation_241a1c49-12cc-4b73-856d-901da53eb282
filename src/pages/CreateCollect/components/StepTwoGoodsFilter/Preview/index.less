.filter_result {
  padding-bottom: 50px;
  position: relative;
  .check_result_btn_box {
    margin-bottom: 10px;
    padding-bottom: 10px;
    text-align: center;
    background-color: #fff;
  }
  .row {
    display: flex;
    justify-content: space-between;
  }
  .row_center {
    display: flex;
    align-items: center;
  }
  .result_btn{
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    background: #f0f2f5;
    bottom: 10px;
    width: 100%;
    padding: 10px 0 0;
    text-align: center;
    z-index: 102;
  }
  :global {
    .ant-col-3{
      display: flex;
      align-items: center;
    }
    .ant-col-4{
      display: flex;
      align-items: center;
    }
  }
}
.flightsCardTitle {
  font-size: 16px;
  font-weight: 600;
  padding: 22px 0px 10px 0px;
}

.preview_table{
  :global {
    table{
      width: auto;
    }
  }
}