import React from 'react';
import { Input, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
// import api from '@/api';


const { Search } = Input
const SearchForm = (props:any) => {
  const { value, onChange, dataSource, onSearch } = props;

  // 输入框的placehoder
  const searchPlacehoder = (dataSource: any) => {
    if (dataSource === 'HOTEL') {
      return '请输入酒店ID/名称';
    }
    if (dataSource === 'TRAVEL') {
      return '请输入卖家昵称或商品ID/标题';
    }

    if (dataSource === 'SPU') {
      return '请输入SPU';
    }

    return '请输入商品ID';
  };

  // 机票“选品结果”搜索表单
  const renderFLIGHTSearchForm = () => {
    return (
      <Space.Compact>
        <Input
          style={{ width: 280 }}
          placeholder="出发城市,多个用英文逗号隔开"
          value={(value?.split('-') || [])[0]}
          onChange={(e) => {
            const city = value?.split('-') || [];
            const cityValue = city[1] || '';
            if (!e.target.value && !cityValue) {
              onChange('')
            } else {
              onChange(`${e.target.value}-${cityValue}`)
            }
          }}
        />
        <div style={{margin: '0px 4px', lineHeight: '32px' }}>-</div>
        <Input
          style={{ width: 280 }}
          placeholder="到达城市,多个用英文逗号隔开"
          value={(value?.split('-') || [])[1]}
          onChange={(e) => {
            const city = value?.split('-') || [];
            const cityValue = city[0] || '';
            if (!e.target.value && !cityValue) {
              onChange('')
            } else {
              onChange(`${cityValue}-${e.target.value}`)
            }
          }}
          addonAfter={<SearchOutlined onClick={onSearch} />}
        />
      </Space.Compact>
    )
  }

  // 其他物料“选品结果”搜索表单
  const renderOtherSearchForm = () => {
    return (
      <Search
        value={value}
        onChange={(e) => {
          onChange(e.target.value);
        }}
        onSearch={onSearch}
        placeholder={searchPlacehoder(dataSource)}
        style={{ width: 300 }}
      />
    )
  }

  return (
    <div>
      {
        dataSource === 'FLIGHT' ? renderFLIGHTSearchForm() : renderOtherSearchForm()
      }
    </div>
  )
}

export default SearchForm;
