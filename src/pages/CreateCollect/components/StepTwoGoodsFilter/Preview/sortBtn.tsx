import React from 'react';
import { Space, Button } from 'antd';

const SortBtn = (props: any) => {
  const { dataSource, fetchPreview,setSortType, setPageNo } = props;

  const renderOthersBtn = () => {
    return (
      <Space>
        <Button
          onClick={() => {
            fetchPreview('', 1);
            setSortType('');
            setPageNo(1);
          }}
        >
          综合排序
        </Button>
        <Button
          onClick={() => {
            fetchPreview('soldRecent', 1);
            setSortType('soldRecent');
            setPageNo(1);
          }}
        >
          销量从高到低
        </Button>
        <Button
          onClick={() => {
            fetchPreview('reservePrice', 1);
            setSortType('reservePrice');
            setPageNo(1);
          }}
        >
          价格从高到低
        </Button>
        {/* <Button
          onClick={() => {
            fetchPreview('tagSortPre', 1);
            setSortType('tagSortPre');
            setPageNo(1);
          }}
        >
          标签关联分排序
        </Button> */}
      </Space>
    )
  }

  const renderFLIGHTBtn = () => {
    return (
      <Space>
        <Button
          onClick={() => {
            fetchPreview('price', 1);
            setSortType('price');
            setPageNo(1);
          }}
        >
          价格从低到高
        </Button>
        <Button
          onClick={() => {
            fetchPreview('discount', 1);
            setSortType('discount');
            setPageNo(1);
          }}
        >
          折扣从低到高
        </Button>
      </Space>
    )
  };

  const renderBtn = () => {
    switch(dataSource) {
      case 'LP':
      case 'HOTEL':
      case 'SHOP':
      case 'POI':
      case 'SPU':
        return null;
      case 'FLIGHT':
        return renderFLIGHTBtn();
      default:
        return renderOthersBtn();
    }
  }


  return (
    <div>
      {renderBtn()}
    </div>
  )
};

export default SortBtn;
