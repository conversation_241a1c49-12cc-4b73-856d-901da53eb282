.filter-modal-content {
  display: flex;
  .right-part {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    .right-top {
      display: flex;
      .right-top-left {
        min-height: 300px;
      }
      .right-top-right {
        display: flex;
        align-items: flex-start;
        .rule-list {
          display: flex;
          flex-direction: column;
          padding: 10px;
          .ant-checkbox-wrapper {
            margin-left: 0;
          }
        }
      }
    }
    .right-middle {
      display: flex;
      flex-wrap: wrap;
      padding: 10px;
      border-top: 1px solid #f0f0f0;
      border-bottom: 1px solid #f0f0f0;
    }
    .right-bottom {
      display: flex;
      flex-grow: 1;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
    }
  }
}