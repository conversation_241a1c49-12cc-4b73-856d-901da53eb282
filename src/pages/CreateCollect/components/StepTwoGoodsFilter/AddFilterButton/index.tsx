import React, { useState, useEffect } from 'react';
import { Button, Modal, Checkbox, Menu, Select, Tag } from 'antd';
import { useGlobal } from '@ali/use-global';

import api from '@/api';
import { getReactKey, unique } from '@/utils/utils';

import './index.less';
import styles from './addStyle.less'

type SingleOption = { value: string; id: number; childNode?: any };

type AvailableRule = {
  actCount: number;
  gmtCreate: string;
  gmtModified: string;
  id: number;
  opretor: string;
  ruleCategory: string;
  ruleDisplayName: string;
  ruleDom: string;
  ruleName: string;
  ruleOp: string;
  ruleType: number;
  ruleValue: string;
  ruleValueType: string;
  status: number;
  valueTemplate: string;
  /** 所属目录的索引 */
  propType: string;
};

const AddFilterButton = (props: { onChange: (data: any) => void; ruleConfigList: any[] }) => {
  const { onChange, ruleConfigList } = props;

  const [global] = useGlobal();

  const [visible, setVisible] = useState(false);
  /** 第一级目录 */
  const [firstLevel, setFirstLevel] = useState<string>('');
  /** 第二级目录 */
  const [secondLevel, setSecondLevel] = useState<string>('');
  const [thirdLevel, setThirdLevel] = useState<string>('');
  const [originRuleList, setOriginRuleList] = useState<AvailableRule[]>([]);
  const [selectedRules, setSelectedRules] = useState<any[]>([]); //当前打开
  const [showRules, setShowRules] = useState<any[]>([]); //显示规则组

  // 请求得到规则树的数据
  const [rulesTree, setRulesTree] = useState<any[]>([]);

  // 判断是否为菜单点击
  const [isMenu, setIsMenu] = useState<boolean>(false);

  // 搜索值
  const [searchVal, setSearchVal] = useState<string>('');

  useEffect(() => {
    if (visible && global?.activity?.dataSource) {
      api.rule
        .getRuleLevelTree({
          dataType: global.activity.dataSource,
        })
        .then((res) => {
          if (res.code === '200' && res.msg === '操作成功') {
            // 第一层的节点是根结点，id为1
            setRulesTree(res.data.childNode || []);
          } else {
            setRulesTree([]);
          }
        });
      api.rule
        .list({
          category: global.activity.dataSource,
        })
        .then((res) => {
          // 只保留有这个propType字段的规则
          setOriginRuleList(res.filter((e) => e.propType));
        });
    }
  }, [visible, global?.activity?.dataSource]);

  useEffect(() => {
    setSelectedRules(ruleConfigList);
    setShowRules(ruleConfigList);
  }, [ruleConfigList]);

  useEffect(() => {
    if (secondLevelNodes.length > 0 && isMenu) {
      setSecondLevel(secondLevelNodes[0].id.toString());
    } else {
      // 当第二步没有值时显示为空
      setSecondLevel('')
      setThirdLevel('')
    }
  }, [firstLevel]);

  useEffect(() => {
    if (thirdLevelNodes.length > 0 && isMenu) {
      setThirdLevel(thirdLevelNodes[0].id.toString());
    } 
  }, [secondLevel]);

  useEffect(() => {
    if (visible) {
      if (rulesTree.length > 0) {
        const firstLevelType: SingleOption[] = rulesTree.map((e) => {
          return {
            value: e.value,
            id: e.id,
          };
        });
        setIsMenu(true);
        setFirstLevel(firstLevelType[0].id.toString());
      }
    }
  }, [visible,rulesTree]);




  const onClose = () => {
    setVisible(false);
    setSelectedRules(showRules);
  };

  // 第一层的节点
  const firstLevelNodes: SingleOption[] = rulesTree.map((e) => {
    return {
      value: e.value,
      id: e.id,
    };
  });

  // 第二层的子节点，默认使用第一个节点的数据
  let secondLevelNodes: SingleOption[] = [];

  if (firstLevel) {
    secondLevelNodes = ((rulesTree.find((e) => e.id === Number(firstLevel)) || {}).childNode || []).map((e: any) => ({
      value: e.value,
      id: e.id,
      childNode: e.childNode,
    }));
  }

  let thirdLevelNodes: SingleOption[] = [];

  // 第二层选中的节点
  if (secondLevel) {
    thirdLevelNodes = (secondLevelNodes.find((e) => e.id === Number(secondLevel)) || { childNode: [] }).childNode.map(
      (e: any) => ({
        value: e.value,
        id: e.id,
      }),
    );
    // setSecondLevel(e.key);
  }

  // 可以勾选的规则
  let availableRules: AvailableRule[] = [];

  if (thirdLevel) {
    availableRules = originRuleList.filter((e: any) => e.propType === `${thirdLevel}`);
  }
  
  return (
    <>
      <Button
        onClick={() => {
          setVisible(true);
        }}
        type="link"
      >
        添加筛选条件
      </Button>
      <Modal
        className={styles.addFilteModal}
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginRight: 30,
              // backgroundColor: '#001529',
              color: 'white',
            }}
          >
            <div>选择筛选条件</div>
            <Select
              value={searchVal}
              showSearch
              placeholder="输入关键字查询筛选条件"
              style={{ width: 240 }}
              filterOption={(input, option) => {
                return (option?.label as string).toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
              options={originRuleList.map((e) => {
                return {
                  label: e.ruleDisplayName,
                  value: e.id,
                  option: e,
                };
              })}
              onChange={(e, item) => {
                // console.log('e00',e,item)
                setIsMenu(false);
                // console.log('selectedRules', selectedRules);
                const { propType, id } = item.option;
                console.log('item.>>>>>',item)
                const [l1, l2, l3, l4] = propType.split('');
                setFirstLevel(`${l1}${l2}`);
                setSecondLevel(`${l1}${l2}${l3}`);
                setThirdLevel(`${l1}${l2}${l3}${l4}`);
                // 如果搜索的规则是没选过的，则自动选中
                if (!selectedRules.find((e) => e.id === id)) {
                  // 点击选中
                  // onChange([
                  //   ...selectedRules,
                  //   {
                  //     ...item.option,
                  //     reactKey: getReactKey(), // 增加一个reactKey
                  //   },
                  // ]);
                  setSelectedRules([
                    ...selectedRules,
                    {
                      ...item.option,
                      reactKey: getReactKey(), // 增加一个reactKey
                    },
                  ]);
                }
                setSearchVal(e)
              }}
            />
          </div>
        }
        width="70vw"
        visible={visible}
        onCancel={() => {
          onClose();
          setSearchVal('')
        }}
        bodyStyle={{ padding: 0 }}
        footer={null}
      >
        <div className="filter-modal-content">
          <Menu
            theme="dark"
            selectedKeys={[firstLevel]}
            onClick={(e) => {
              setFirstLevel(e.key);
              setIsMenu(true);
            }}
          >
            {firstLevelNodes.map((e) => {
              return <Menu.Item key={e.id}>{e.value}</Menu.Item>;
            })}
          </Menu>
          <div className="right-part">
            <div className="right-top">
              <div className="right-top-left">
                <Menu
                  theme="light"
                  selectedKeys={[secondLevel]}
                  onClick={(e) => {
                    setSecondLevel(e.key);
                    setIsMenu(true);
                  }}
                >
                  {secondLevelNodes.map((e) => {
                    return <Menu.Item key={e.id}>{e.value}</Menu.Item>;
                  })}
                </Menu>
              </div>
              <div className="right-top-right">
                <Menu
                  theme="light"
                  selectedKeys={[thirdLevel]}
                  onClick={(e) => {
                    setThirdLevel(e.key);
                    setIsMenu(true);
                  }}
                >
                  {thirdLevelNodes.map((e) => {
                    return <Menu.Item key={e.id}>{e.value}</Menu.Item>;
                  })}
                </Menu>
                <div className="rule-list">
                  {availableRules.map((e,index) => {
                    return (
                      <Checkbox
                        key={e.id}
                        checked={selectedRules.map((el) => el.id).includes(e.id)}
                        onChange={(ele) => {
                          if (Number(searchVal) === e.id) {
                            setSearchVal('')
                          }
                          if (ele.target.checked) {
                            setSelectedRules([...selectedRules, e]);
                          } else {
                            setSelectedRules(selectedRules.filter((i) => i.id !== e.id));
                          }
                        }}
                      >
                        {e.ruleDisplayName}
                      </Checkbox>
                    );
                  })}
                </div>
              </div>
            </div>
            <div className="right-middle">
              已选择
              {unique(selectedRules,'id').map((e) => {
                return (
                  <Tag
                    key={e.id}
                    closable
                    onClose={() => {
                      setSelectedRules(selectedRules.filter((i) => i.id !== e.id));
                    }}
                  >
                    {e.ruleDisplayName}
                  </Tag>
                );
              })}
            </div>
            <div className="right-bottom">
              <div>{selectedRules.length > 0 && selectedRules[selectedRules.length - 1].description}</div>
              <Button
                type="primary"
                onClick={() => {

                  // setShowRules(selectedRules)
                  // 去外面修改的就是一个规则组里的规则列表
                  onChange(
                    selectedRules.map((e, idx) => {
                      let newRuleValue = e.ruleValue;
                      // ruleName为validDate时，填默认值1
                      if (e.ruleName === 'validDate' && !e.ruleValue) {
                        newRuleValue = ',,1';
                      }
                      if (e.ruleName === 'noTravelDate' && !e.ruleValue) {
                        newRuleValue = ',,0';
                      }

                      return {
                        ...e,
                        ruleValue: newRuleValue,
                        reactKey: `${getReactKey()}-${idx}`, // 增加一个reactKey
                      };
                    }),
                  );
                  onClose();
                  setSearchVal('')
                }}
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AddFilterButton;
