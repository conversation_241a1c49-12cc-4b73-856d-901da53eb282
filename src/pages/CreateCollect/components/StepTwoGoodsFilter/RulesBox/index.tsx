import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Space, Button, Modal, message } from 'antd';
import { DeleteOutlined, CloseOutlined } from '@ant-design/icons';
import { cloneDeep } from 'lodash';

import { getComponentByType } from '@/components/Rules/index';

import AddFilterButton from '../AddFilterButton';

import { getReactKey } from '@/utils/utils';
import DefaultRule from '@/components/Rules/DefaultRule';

const RulesBox = (props: {
  rules: any;
  onChange: (data: any) => void;
  filterType: any;
  createFlag: any;
  existVal: any;
  uploadType: any;
  dataSource: string;
}) => {
  const [height, setHeight] = useState(0); //计算或的高度
  const { rules, onChange, filterType, createFlag, existVal, uploadType, dataSource } = props;

  const measuredRef = useCallback(
    (node) => {
      if (node !== null) {
        setHeight(node.getBoundingClientRect().height - 84);
      }
    },
    [rules],
  );

  // 清空无rom

  const emptyRuleValue = (subRules: any) => {
    subRules.forEach((el: any) => {
      if (el.subRules) {
        emptyRuleValue(el.subRules);
      } else {
        el.ruleValue = '';
      }
    });
  };

  const renderSingleRuleGroup = (ruleGroup: any, index: number) => {
    const { ruleConfigList, reactKey: ruleGroupReactKey } = ruleGroup;
    return (
      <div className="single-rule-group" key={ruleGroupReactKey}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div className="title">{`规则组${index + 1}`}</div>
          <div
            className="delete-icons"
            style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}
          >
            <Button
              type="link"
              onClick={(e) => {
                onChange(rules.filter((t) => t.reactKey !== ruleGroupReactKey));
              }}
              icon={<DeleteOutlined />}
            />
          </div>
        </div>
        <div className="content">
          <div className="items">
            {ruleConfigList.map((ruleConfig: any, ruleIdx: number) => {
              return (
                <div className="rule-item" key={ruleConfig.reactKey}>
                  <Space>
                    <div style={{ paddingLeft: 50, textAlign: 'right' }}>{ruleConfig.ruleDisplayName}:</div>
                    {ruleConfig.ruleDom ? (
                      getComponentByType({
                        ruleConfig,
                        onChange: (value: string) => {
                          const isCommon = [
                            'INPUT',
                            'TAG_INPUT',
                            'CHECKBOX',
                            'RADIO',
                            'SELECTOR',
                            'doubleSelect',
                            'linePlayLabel',
                            'TREE',
                            'CASCADER',
                            'TVSBC_I',
                            'TVSBC_S',
                            'MULTISELECT',
                            'PERIPHERY',
                            'DATE',
                            'TEXT'
                          ].includes(ruleConfig.ruleDom);
                          if (isCommon) {
                            ruleConfigList[ruleIdx].ruleValue = value;
                            const findIdx = rules.findIndex(
                              (ruleGroup: any) => ruleGroup.reactKey === ruleGroupReactKey,
                            );
                            rules[findIdx].ruleConfigList = ruleConfigList;
                            onChange(cloneDeep(rules));
                          }
                        },
                      })
                    ) : (
                      <DefaultRule
                        rule={ruleConfig}
                        onChange={(value: any) => {
                          ruleConfigList[ruleIdx] = value;
                          const findIdx = rules.findIndex((ruleGroup: any) => ruleGroup.reactKey === ruleGroupReactKey);
                          rules[findIdx].ruleConfigList = ruleConfigList;
                          onChange(cloneDeep(rules));
                        }}
                      />
                    )}
                  </Space>
                  <Space>
                    <Button
                      type="link"
                      disabled={ruleConfig?.ruleName === 'rpPoolId'}
                      onClick={() => {
                        const findIdx = rules.findIndex((ruleGroup: any) => ruleGroup.reactKey === ruleGroupReactKey);
                        const checkedRule = rules[findIdx].ruleConfigList.find(
                          (e) => e.reactKey === ruleConfig.reactKey,
                        );
                        const newCheckedRule = cloneDeep(checkedRule);
                        newCheckedRule.reactKey = `${getReactKey()}-${index}-${ruleIdx}`;
                        // 区分是否有ruleDom
                        if (ruleConfig.ruleDom) {
                          newCheckedRule.ruleValue = '';
                          if (ruleConfig.ruleName === 'validDate') {
                            newCheckedRule.ruleValue = ',,1';
                          }
                          if (ruleConfig.ruleName === 'noTravelDate') {
                            newCheckedRule.ruleValue = ',,0';
                          }
                        } else {
                          emptyRuleValue(newCheckedRule.subRules);
                        }
                        rules[findIdx].ruleConfigList.splice(ruleIdx + 1, 0, newCheckedRule);
                        onChange(cloneDeep(rules));
                      }}
                    >
                      复制
                    </Button>
                    <Button
                      type="link"
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        const modal = Modal.confirm({
                          title: '确认删除该规则？',
                          // content: '确认删除该规则？',
                          okText:"确定",
                          cancelText:'取消',
                          onOk: () => {
                            const findIdx = rules.findIndex(
                              (ruleGroup: any) => ruleGroup.reactKey === ruleGroupReactKey,
                            );
                            rules[findIdx].ruleConfigList = ruleConfigList.filter(
                              (e) => e.reactKey !== ruleConfig.reactKey,
                            );
                            onChange(cloneDeep(rules));
                            modal.destroy();
                          },
                          onCancel: () => {
                            modal.destroy();
                          },
                        });
                      }}
                    />
                  </Space>
                </div>
              );
            })}
          </div>
          {ruleConfigList.length > 1 && (
            <div className="and-icon">
              <div className="icon-line" style={{ height: 'calc(100% - 40px)' }}></div>
              <span>且</span>
            </div>
          )}
        </div>
        <Space>
          <AddFilterButton
            ruleConfigList={ruleConfigList}
            onChange={(ruleConfigList: any[]) => {
              rules[index].ruleConfigList = ruleConfigList;
              console.log('ruleConfigLi',ruleConfigList)
              onChange(cloneDeep(rules));
            }}
          />
        </Space>
      </div>
    );
  };

  return (
    <div className="rules-box">
      <div className="rules-container">
        <div ref={measuredRef} style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ width: '100%' }}>{rules.length > 0 && rules.map(renderSingleRuleGroup)}</div>
          {rules.length > 1 && (
            <div className="or-icon">
              <div className="icon-line" style={{ height }}></div>
              <span>或</span>
            </div>
          )}
        </div>
        <Button
          style={{ marginTop: 10 }}
          type="primary"
          disabled={dataSource === 'FLIGHT' && rules?.length > 0}
          onClick={() => {
            //盘点货品要求先上传模版才
            if (filterType === '3') {
              if (uploadType === 'excel' || uploadType === 'odps') {
                if (!createFlag) {
                  message.error('请先上传');
                  return;
                }
              }
              if (uploadType === 'existCollection') {
                if (!existVal) {
                  message.error('请选择盘点品集');
                  return;
                }
              }
            }
            onChange([...rules, { ruleOp: 'AND', reactKey: getReactKey(), ruleConfigList: [] }]);
          }}
        >
          添加规则组
        </Button>
      </div>
    </div>
  );
};

export default RulesBox;
