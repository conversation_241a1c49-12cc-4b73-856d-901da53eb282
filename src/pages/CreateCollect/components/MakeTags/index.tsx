import { useEffect, useState } from 'react';
import { Tag, DatePicker, Button, Card, Select, message, Spin, Popconfirm } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import moment from 'moment';
import { useGlobal } from '@ali/use-global';
import _ from 'lodash';

import { updateHash, getUrlParams } from '@/utils/utils';

import { CollectStatusEnum } from '../../constants';

import api from '@/api';

import './index.less';
const { Option } = Select;

type SelectTag = { label: string; value: number };

const MakeTags = (props: any) => {
  const { editMakeTags = true } = props;
  const urlParams = getUrlParams();

  const [tagList, setTagList] = useState([]);
  const [selectedTags, setSelectedTags] = useState<SelectTag[]>([]);
  const [loading, setLoading] = useState(false);

  const [tagLoading, setTagLoading] = useState(false);

  const [pushTags, setPushTags] = useState<any[]>([]);

  const [oldTags, setOldTags] = useState<any[]>([]); //老的存在的值

  const [popFlag, setPopFlag] = useState(true);

  const [global] = useGlobal();

  // 如果global中没有这个，表示是url重入的，要自动跳到上一页
  if (!global.hasOwnProperty('itemCount')) {
    updateHash({
      params: {
        ...urlParams,
        step: CollectStatusEnum.SETCOLLECTCONDITION,
      },
    });
  }

  useEffect(() => {
    if (urlParams.collectionId) {
      api.activity
        .tagList({
          activityId: urlParams.collectionId,
          owner: global?.activity?.activityOwner,
        })
        .then((res) => {
          if (res.code === '200' && res.msg === '操作成功') {
            setTagList(
              res.data.map((e) => ({
                label: e.tagName,
                value: e.id,
              })),
            );
          }
        });
    }
  }, [urlParams.collectionId, global?.activity?.activityOwner]);

  useEffect(() => {
    if (urlParams.collectionId) {
      api.activity
        .GetActivityTagList({
          id: urlParams.collectionId,
        })
        .then((res) => {
          if (res.code === '200' && res.msg === '操作成功') {
            setSelectedTags(
              (res.data || []).map((ele: any) => {
                return {
                  label: ele.tagName,
                  value: ele.id,
                };
              }),
            );
            setOldTags(
              (res.data || []).map((ele: any) => {
                return {
                  label: ele.tagName,
                  value: ele.id,
                };
              }),
            );
          } else {
            message.error(res.msg);
          }
        });
    }
  }, [urlParams.collectionId, global?.activity?.activityOwner]);

  // 删除tag
  const removeTag = (value: any) => {
    // 删除老的tag调接口，删除新加的不调接口
    if (oldTags.some((e) => e.value === value)) {
      setTagLoading(true);
      api.activity
        .RemoveActivityTag({
          id: urlParams.collectionId,
          tagId: value,
          deleteTagNow: true,
        })
        .then((res) => {
          if (res.code === '200') {
            setTagLoading(false);
            setSelectedTags(selectedTags.filter((i) => i.value !== value));
            setPushTags(pushTags.filter((i) => i.value !== value));
            setOldTags(oldTags.filter((i) => i.value !== value));
            message.success(res.msg);
          } else {
            setTagLoading(false);
            message.error(res.msg);
          }
        });
    } else {
      setSelectedTags(selectedTags.filter((i) => i.value !== value));
      setPushTags(pushTags.filter((i) => i.value !== value));
      setOldTags(oldTags.filter((i) => i.value !== value));
    }
  };

  const jumpToNextStep = () => {
    // 跳转到下一步
    updateHash({
      params: {
        type: urlParams.type, // 选品类型FilterType
        step: CollectStatusEnum.PUBLISHCOLLECT,
        collectionId: urlParams.collectionId, // 创建成功的品集id
        isFrame: window.location.href.includes('isFrame=true') ? true : false,
      },
    });
  };

  // 打标页面返回的上一步，必然是选品页面
  const goBack = () => {
    updateHash({
      params: {
        type: urlParams.type, // 选品类型FilterType
        step: CollectStatusEnum.SETCOLLECTCONDITION,
        collectionId: urlParams.collectionId, // 创建成功的品集id
        isFrame: window.location.href.includes('isFrame=true') ? true : false,
      },
    });
  };

  const excutePublish = async () => {
    setLoading(true);
    const res = await api.activity.publish({
      id: urlParams.collectionId,
      itemCount: global.itemCount,
      tags: pushTags.map((e) => e.value).join(','),
    });
    setLoading(false);
    if (res.code === '200' && res.msg === '操作成功') {
      // 调用接口之后，进入下一步
      jumpToNextStep();
    }
  };

  return (
    <div className="make-tags">
      <Spin spinning={loading}>
        <Card title="货品打标" bordered={false}>
          <div className="form-container">
            <div className="row">
              <div className="row-title">货品标签：</div>
              <div className="row-content">
                <Select
                  // mode="multiple"

                  // disabled={selectedTags.indexOf(item.id) >= 0}
                  // value={selectedTags.map((e) => e.value)}
                  style={{ width: 200 }}
                  // options={tagList}
                  onChange={(data, item) => {
                    setSelectedTags([...selectedTags, { label: item.children, value: item.value }]);
                    setPushTags([...pushTags, { label: item.children, value: item.value }]);
                  }}
                  disabled={!editMakeTags}
                >
                  {tagList &&
                    tagList.length > 0 &&
                    tagList.map((ele: any) => (
                      <Option disabled={selectedTags.some((el) => el.value === ele.value)} value={ele.value}>
                        {ele.label}
                      </Option>
                    ))}
                </Select>
              </div>
            </div>
            <div className="row">
              若无可选标签，则标签未关联或未审核，已关联未审核请联系「韭花」，若未关联则去ICTag关联页进行「关联」
            </div>
            <div className="row">
              <Spin spinning={tagLoading}>
                {selectedTags.map((e) => (
                  <Tag
                    key={e.value}
                    closable={editMakeTags}
                    closeIcon={
                      <Popconfirm
                        title="是否要删除此标签"
                        onConfirm={() => {
                          removeTag(e.value);
                        }}
                      >
                        <CloseOutlined />
                      </Popconfirm>
                    }
                    onClose={(el) => {
                      el.preventDefault();
                    }}
                    onChange={() => {
                      setPopFlag(false);
                    }}
                  >
                    {e.label}
                  </Tag>
                ))}
              </Spin>
            </div>
          </div>
        </Card>
        <div className="bottom-btns">
          <Button onClick={goBack} style={{ width: 88 }}>
            上一步
          </Button>
          <Button type="primary" style={{ width: 88 }} onClick={excutePublish}>
            执行发布
          </Button>
        </div>
      </Spin>
    </div>
  );
};

export default MakeTags;
