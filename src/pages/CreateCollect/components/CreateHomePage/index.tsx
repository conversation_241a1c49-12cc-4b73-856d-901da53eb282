import React from 'react';

import createListImg1 from '@/components/Images/createListImg1.png';
import createListImg2 from '@/components/Images/createListImg2.png';
import createListImg3 from '@/components/Images/createListImg3.png';
import createListImg4 from '@/components/Images/createListImg4.png';

import { FilterType } from '../../constants';
import { Button } from 'antd';
import { history } from 'umi';

const createList = [
  {
    imgSrc: createListImg1,
    title: '自由选品',
    describe: '通过商品基础属性、商品标签、人群属性、业务指标圈选货品',
    btnName: '创建品集',
    type: FilterType.FreeSelect,
  },
  {
    imgSrc: createListImg2,
    title: '上传品集',
    describe: '通过上传Excle、Odps表，生产线上品集，品集可用于投放或分析',
    btnName: '一键上传',
    type: FilterType.Upload,
  },
  {
    imgSrc: createListImg3,
    title: '盘点货品',
    describe: '通过上传品集，或选择已创建的品集，进行盘点分析',
    btnName: '一键盘货',
    type: FilterType.Inventory,
  },
  // {
  //   imgSrc: createListImg4,
  //   title: '问题自查',
  //   describe: '快速了解品池中商品被过滤的原因，结合原因进行后续的商品处理',
  //   btnName: '一键查询',
  //   type: FilterType.Inventory,
  // },
  // {
  //   imgSrc: createListImg2,
  //   title: '规则接入',
  //   describe: '业务通过odps和rpc接口、mq消息来完成规则的创建和数据更新',
  //   btnName: '一键接入',
  //   type: FilterType.Inventory,
  // },
];

const Btn = (i:number,ele: any, createBtn: (type: FilterType) => React.ReactElement ) => {
  switch (i) {
    case 0:
        return createBtn(ele.type)
    case 1:
        return createBtn(ele.type)
    case 2:
        return createBtn(ele.type)
    case 3:
        return <Button type="primary" shape="round" onClick={() => {
          history.push('/tool/filterquery')
        }}>
          {ele.btnName}
        </Button>
    case 4:
      return <Button type="primary" shape="round" onClick={() => {
        history.push('/createRule')
      }}>
        {ele.btnName}
      </Button>
    default:
      break;
  }
}

const CreateHomePage = (props: { createBtn: (type: FilterType) => React.ReactElement }) => {
  const { createBtn } = props;
  return (
    <>
      <h3 className="create-collect-h3">创建品集</h3>
      <div className="create-collect-main">
        {createList.map(
          (
            ele: {
              title: string;
              imgSrc: string;
              describe: string;
              type: FilterType;
              btnName: string;
            },
            i: number,
          ) => {
            return (
              <div className="create-collect-list" key={i}>
                <div className="create-collect-img">
                  <img src={ele.imgSrc} />
                </div>
                <div className="create-collect-title">{ele.title}</div>
                <div className="create-collect-describe">{ele.describe}</div>
                {Btn(i,ele,createBtn)}
              </div>
            );
          },
        )}
      </div>
    </>
  );
};

export default CreateHomePage;
