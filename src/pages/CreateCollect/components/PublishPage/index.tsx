import { useState, useEffect, useRef } from 'react';
import { Result, Button, message } from 'antd';
import moment from 'moment';

import TimeCounter, { formatTime } from './TimeCounter';
import { getUrlParams, updateHash } from '@/utils/utils';
import api from '@/api';

import { ActivityStatus, CollectStatusEnum } from '../../constants';

const PublishPage = () => {
  const urlParams = getUrlParams();
  const [status, setStatus] = useState<number>();
  const [vipPublishParams, setVipPublishParams] = useState<any>();
  const [gmtModified, setGmtModified] = useState('');
  const [speedCount, setSpeedCount] = useState(null);
  const [publishTime, setPublishTime] = useState(0);
  // 是否展示加速发布按钮
  const [speedUpAvailable, setSpeedUpAvailable] = useState(false);
  // 轮询标识
  const [updateTag, setUpdateTag] = useState(0);
  // 发布失败原因
  const [buildErrorMsg, setBuildErrorMsg] = useState('');

  useEffect(() => {
    // 通过这个接口获取发布所需时间
    // 剩余发布时间，开始发布时间
    updateActivityInfo();
  }, []);

  useEffect(() => {
    // 如果发布失败，停止轮询
    if (status === ActivityStatus.PUBLISH_FAILED) {
      return;
    }
    // 每15秒轮询一次
    setTimeout(() => {
      updateActivityInfo();
      setUpdateTag((x) => x + 1);
    }, 15000);
  }, [updateTag, status]);

  const updateActivityInfo = () => {
    api.activity
      .getActivity({
        id: urlParams.collectionId,
        fetch: false,
      })
      .then((res: any) => {
        if (res.code === '200' && res.msg === '操作成功') {
          setStatus(res.data.activity.status);
          // 后端返回发布需要的时间
          setPublishTime(res.data.publishTime || 0);
          setGmtModified(res.data.activity.gmtModified);
          // 返回的点击加速发布的次数
          if (res.data.speedCount) {
            setSpeedCount(speedCount);
          }
          // 获取发布失败原因
          if (res.data.activity.extInfo && res.data.activity.extInfo.buildErrorMsg) {
            setBuildErrorMsg(res.data.activity.extInfo.buildErrorMsg);
          }
          setVipPublishParams({
            id: res.data.activity.id,
            itemCount: res.data.activity.itemCount,
            tags: res.data.activity.tags,
            vipPublish: true,
          });
        }
      });
  };

  useEffect(() => {
    // 状态为1，说明发布已完成
    if (status === ActivityStatus.VALID) {
      // console.log('跳转到完成选品页面');
      updateHash({
        params: {
          type: urlParams.type, // 选品类型FilterType
          step: CollectStatusEnum.FINISH,
          collectionId: urlParams.collectionId, // 创建成功的品集id
          isFrame: window.location.href.includes('isFrame=true') ? true : false,
        },
      });
    }
    // 发布中
    if (status === ActivityStatus.PUBLISHING && gmtModified) {
      console.log('gmtModified', gmtModified);
      // 返回的发布时间
      const x = moment(gmtModified);
      // 当前的时间
      const y = moment();
      // 时间差值
      const duration = y.diff(x, 'seconds');
      console.log('duration', duration);
      // console.log('vipPublishParams?.itemCount', vipPublishParams?.itemCount);
      // 如果商品数小于2万，且已经等待的时间超过10分钟（600秒），则显示加速发布按钮
      if (duration > 600 && vipPublishParams?.itemCount < 20000) {
        setSpeedUpAvailable(true);
      }
    }
    // 如果时间为0，表示发布成功
  }, [status, gmtModified, vipPublishParams?.itemCount]);

  const renderExtra = () => {
    if (speedUpAvailable) {
      return (
        <div>
          着急使用，点击
          <Button
            type="link"
            onClick={() => {
              // console.log('加速发布');
              if (speedCount) {
                message.warning('当前限制只能加速一次噢！着急请联系长弘');
                return;
              }
              api.activity.publish(vipPublishParams).then((res: any) => {
                if (res.code === '200') {
                  message.success('已开始加速发布');
                  api.activity
                    .getActivity({
                      id: urlParams.collectionId,
                      fetch: false,
                    })
                    .then((res: any) => {
                      if (res.code === '200' && res.msg === '操作成功') {
                        setStatus(res.data.activity.status);
                        setGmtModified(res.data.activity.gmtModified);
                        if (res.data.speedCount) {
                          setSpeedCount(speedCount);
                        }
                        setVipPublishParams({
                          id: res.data.activity.id,
                          itemCount: res.data.activity.itemCount,
                          tags: res.data.activity.tags,
                          vipPublish: true,
                        });
                      }
                    });
                } else {
                  message.error(res.msg);
                }
              });
            }}
          >
            加速发布
          </Button>
        </div>
      );
    }

    // 如果发布时间超过10分钟，显示加速发布
    return <div>正在努力发布，请小可爱稍作等待</div>;
  };

  const getPublishTimeout = () => {
    const x = moment(gmtModified);
    // 当前的时间
    const y = moment();
    // 时间差值
    const duration = y.diff(x, 'seconds');
    // 返回已发布时间大于预计发布时间
    return duration > publishTime;
  };

  // 返回圈品页面
  const goBackToFilter = () => {
    updateHash({
      params: {
        type: urlParams.type, // 选品类型FilterType
        step: CollectStatusEnum.SETCOLLECTCONDITION,
        collectionId: urlParams.collectionId, // 创建成功的品集id
        isFrame: window.location.href.includes('isFrame=true') ? true : false,
      },
    });
  };

  // 发布失败的显示内容
  if (status === ActivityStatus.PUBLISH_FAILED) {
    return (
      <div style={{ background: '#fff' }}>
        <Result
          status="error"
          title="发布失败"
          subTitle={
            <div>
              <div style={{ marginBottom: 16 }}>发布过程中出现了错误，请查看详细信息：</div>
              {buildErrorMsg && (
                <div style={{ 
                  background: '#fff2f0', 
                  border: '1px solid #ffccc7', 
                  borderRadius: 4, 
                  padding: 12,
                  textAlign: 'left',
                  color: '#ff4d4f'
                }}>
                  {buildErrorMsg}
                </div>
              )}
            </div>
          }
          extra={[
            <Button key="back-to-filter" type="primary" onClick={goBackToFilter}>
              返回圈品
            </Button>
          ]}
        />
      </div>
    );
  }

  // 正常发布中的显示内容
  return (
    <div style={{ background: '#fff' }}>
      <Result
        title={
          <div>
            <div>飞速发布中 ～～～～</div>
            <div>预计发布完成所需时间：{formatTime(publishTime)}</div>
            <div>
              当前已等待时间：
              <TimeCounter gmtModified={gmtModified} status={status} />
            </div>
            {getPublishTimeout() && (
              <div style={{ color: 'red' }}>发布时间已超过预计时间，依赖问题请先自己检查，需要等所有上游依赖完成才能继续发布，其他疑问请进答疑群找值班</div>
            )}
            {buildErrorMsg && (
              <div style={{ color: 'red', marginTop: 8 }}>依赖问题：{buildErrorMsg}</div>
            )}
          </div>
        }
        // extra={renderExtra()}
      />
    </div>
  );
};

export default PublishPage;
