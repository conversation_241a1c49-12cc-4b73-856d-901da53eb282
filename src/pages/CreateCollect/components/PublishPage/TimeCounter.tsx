import { useState, useEffect } from 'react';
import { ActivityStatus } from '../../constants';
import moment from 'moment';

export const formatTime = (time: number) => {
  // const day = Math.floor(time / 86400);
  const day = 0;
  const hour = Math.floor((time - day * 86400) / 3600);
  const minute = Math.floor((time - day * 86400 - hour * 3600) / 60);
  const seconds = time - day * 86400 - 3600 * hour - 60 * minute;

  return `${[hour, minute, seconds].map((e) => `${e}`.padStart(2, '0')).join(':')}`;
};

const TimeCounter = (props: { gmtModified: string; status?: number }) => {
  const { gmtModified, status } = props;
  const [timeCount, setTimeCount] = useState(0);

  useEffect(() => {
    // 如果publishTime有值，则赋予timeCount
    if (gmtModified) {
      const x = moment(gmtModified);
      // 当前的时间
      const y = moment();
      // 时间差值
      const duration = y.diff(x, 'seconds');
      setTimeCount(duration);
    }
  }, [gmtModified]);

  useEffect(() => {
    let timer: number;
    // 正在发布中
    if (status === ActivityStatus.PUBLISHING) {
      timer = setTimeout(() => {
        setTimeCount(timeCount + 1);
      }, 1000) as unknown as number;
    }

    return () => {
      clearTimeout(timer);
    };
  }, [status, timeCount]);

  if (status !== ActivityStatus.PUBLISHING) {
    return <></>;
  }

  return <span>{formatTime(timeCount)} </span>;
};

export default TimeCounter;
