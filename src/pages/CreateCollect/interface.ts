interface PreviewCommonData {
  itemId: number;
  headPic: string;
  name: string;
}

/**
 * 旅行预览数据
 */
export interface PreviewTravelData extends PreviewCommonData {
  discountPrice: number;
  reservePrice: number;
  sellerNick: string;
  category: string;
  saleCount: string;
  stringTypeItemId: string;
  id: number;
  tagInfos: {
    tagName: string;
    relScore: string;
  }[];
}

/**
 * 酒店预览数据
 */
export interface PreviewHotelData extends PreviewCommonData {
  supportCredit: string;
  hotelCountry: string;
  hotelCity: string;
  hotelRateAvg: string;
  hotelRateNum: string;
  hotelCheckinQty1m: number;
  selectedHotelCheckinQty1m: number;
  sellerDesc: string;
  sellerInfoList: {
    creditPay: string;
    hid: string;
    other: string;
    sellerName: string;
  }[];
}

/**
 * POI预览数据
 */
export interface PreviewPOIData {
  bigImage: string;
  deleteText: string;
  itemId: number;
  labelList: string[];
}

/**
 * LP预览数据
 */
export interface PreviewLPData extends PreviewCommonData {}

/**
 * RP预览数据
 */
export interface PreviewRPData extends PreviewCommonData {
  shotelName: string;
  shid: number;
}


/**
 * 商家
 */
 export interface PreviewShopData extends PreviewCommonData {
  title: string;
  shopStatus: string;
  shopScope: string;
  shopType: string
}

/**
 * 民宿
 */
export interface PreviewHomeStayData extends PreviewCommonData {
  url: string;
  reservePrice: string|number;
  sellerNick: string;
  score: string|number;
  bnbKsNum: string|number;
}

/**
 * 哥伦布榜单
 */

 export interface PreviewGLBStayData extends PreviewCommonData {
  url: string;
  dest: string|number;
  type: string|number;
  count: number,
  stringTypeItemId: string|number;
}

/**
 * 机票
 */

export interface PreviewFLIGHTStayData extends PreviewCommonData {
  id: number; // 虚拟机票id
  bizType: number; // 国内/国际，0：国内，1：国际
  depCityName: string; // 出发城市
  arrCityName: string; // 到达城市
  flightNo: string; // 航班号
  depAirportName: string; // 出发机场
  arrAirportName: string; // 到达机场
  tripType: number; // 航程类型，0：单程，1：往返
  direct: boolean; // 是否直飞
  sellPrice: number; // 售价，国际机票售价包含税
  tax: number; // 税
  discountInfo: string; // 折扣，国际机票没有折扣
  depTime: string; // 出发时间
  arrTime: string; // 到达时间
}

/**
 * SPU
 */

export interface PreviewSPUStayData extends PreviewCommonData {
  spuTitle: string; // spu名称
  itemNum: number; // 商品数量
  spuLabels: string; //标签
  containPoi: string; //包含poi
  containCity: string; //包含城市
  spuDays: number; //SPU天数
}
