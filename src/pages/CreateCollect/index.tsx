import React, { useState, useEffect } from 'react';
import { But<PERSON>, Result } from 'antd';
import { useGlobal } from '@ali/use-global';

import CreateSteps from './components/CreateSteps';
import CreateHomePage from './components/CreateHomePage';
import StepOneCreateCollection from './components/StepOneCreateCollection';
import StepOneCreateSelection from './components/StepOneCreateSelection';
import StepTwoGoodsFilter from './components/StepTwoGoodsFilter';
import MakeTags from './components/MakeTags';

import { FilterType, CollectStatusEnum } from './constants';
import { updateHash, getUrlParams } from '@/utils/utils';

import PublishPage from './components/PublishPage';
import api from '@/api';

import { history } from 'umi';

import './index.less';

const CreateCollect = () => {
  // 用来控制展示哪个子组件
  const [status, setStatus] = useState(CollectStatusEnum.START);
  const [filterType, setFilterType] = useState<FilterType>(FilterType.FreeSelect);
  const [isShowTag, setIsShowTag] = useState<number>(1);
  const [editMakeTags, setEditMakeTags] = useState<boolean>(true);

  const urlParams = getUrlParams();
  const [, setGlobal] = useGlobal();

  useEffect(() => {
    if (urlParams.step) {
      // 如果访问了打标的页面，自动跳转到上一页，这个逻辑做在打标页面里面
      setStatus(Number(urlParams.step));
    } else {
      setStatus(CollectStatusEnum.START);
    }
  }, [urlParams.step]);

  useEffect(() => {
    if (urlParams.type) {
      setFilterType(urlParams.type);
    }
  }, [urlParams.type]);

  useEffect(() => {
    // 如果url中有品集id
    if (urlParams.collectionId) {
      setGlobal({ activity: {} });
      api.activity.getActivity({ id: urlParams.collectionId, fetch: false }).then((res) => {
        if (res.code === '200' && res.msg === '操作成功') {
          // 将activity存到全局备用,当前只是要在打标页面使用
          setGlobal({
            activity: res.data.activity,
          });
          setIsShowTag(res.data.activity?.hasTag);
          const editMakeTags = res.data.activity?.hasTag === 1 && res.data.activity?.tags ? false : true;
          setEditMakeTags(editMakeTags);
          if (res.data.activity.status === 4) {
            if (window.location.href.includes('isFrame=true')) {
              history.push(
                `/createCollect?type=${Number(res.data.activity.createType) + 1}&step=4&collectionId=${
                  res.data.activity.id
                }&isFrame=true&activityId=${urlParams?.activityId}&activityTitle=${urlParams?.activityTitle}`,
              );
            } else {
              history.push(
                `/createCollect?type=${Number(res.data.activity.createType) + 1}&step=4&collectionId=${
                  res.data.activity.id
                }`,
              );
            }
          }
        }
      });
    }
  }, [urlParams.collectionId, urlParams.step]);

  const renderCreateBtn = (type: FilterType) => {
    return (
      <Button
        type="primary"
        shape="round"
        onClick={() => {
          updateHash({
            params: {
              type, // 选品类型FilterType
              step: CollectStatusEnum.CREATECOLLECT,
            },
          });
        }}
      >
        {type === FilterType.FreeSelect && '创建品集'}
        {type === FilterType.Upload && '一键上传'}
        {type === FilterType.Inventory && '一键盘货'}
      </Button>
    );
  };

  const renderFinish = () => {
    return (
      <div style={{ background: '#fff' }}>
        <Result
          status="success"
          title="发布成功！"
          extra={[
            <Button
              key="jump-to-steTwo"
              onClick={() => {
                updateHash({
                  params: {
                    type: urlParams.type, // 选品类型FilterType
                    step: CollectStatusEnum.SETCOLLECTCONDITION,
                    collectionId: urlParams.collectionId, // 创建成功的品集id
                    isFrame: window.location.href.includes('isFrame=true') ? true : false,
                  },
                });
              }}
            >{`查看圈品规则`}</Button>,
            <Button
              key="jump-to-result"
              onClick={() => {
                if (window.location.href.includes('isFrame=true')) {
                  history.push(
                    `/createCollect/goodsPool?collectionId=${urlParams.collectionId}&isFrame=true&activityId=${urlParams?.activityId}&activityTitle=${urlParams?.activityTitle}`,
                  );
                } else {
                  history.push(`/createCollect/goodsPool?collectionId=${urlParams.collectionId}`);
                }
              }}
            >{`查看线上商品池`}</Button>,
            !window.location.href.includes('isFrame=true') && (
              <Button
                key="jump-to-list"
                onClick={() => {
                  if (window.location.href.includes('isFrame=true')) return;
                  const beforeHash = window.location.href.split('#')[0];
                  window.location.href = `${beforeHash}/#/manageCollect`;
                }}
              >{`查看列表 > >`}</Button>
            ),
            window.location.href.includes('isFrame=true') && (
              <Button
                key="jump-to-list"
                onClick={() => {
                  window.open(
                    `https://${window.location.host}/#/manageCollect/manageChild?activityTitle=${urlParams?.activityTitle}&id=${urlParams?.activityId}&isChild=1&isOwner=false&pageNos=1&isFrame=true`,
                    '_self',
                  );
                }}
              >{`返回选品池列表`}</Button>
            ),
          ]}
        />
      </div>
    );
  };

  // 计算当前步骤在步骤条中的位置
  const getStepCurrent = () => {
    if (status === CollectStatusEnum.START) return 0;
    if (status === CollectStatusEnum.CREATECOLLECT) return 0;
    if (status === CollectStatusEnum.SETCOLLECTCONDITION) return 1;
    if (status === CollectStatusEnum.MAKETAGS) return 1; // 保持MakeTags和圈选货品在同一步骤
    if (status === CollectStatusEnum.PUBLISHCOLLECT) return 2;
    if (status === CollectStatusEnum.FINISH) return 3;
    return 0;
  };

  return (
    <div className="create-collect-wrap">
      {status > CollectStatusEnum.START && <CreateSteps current={getStepCurrent()} isShowTag={isShowTag} />}
      <div>
        {status === CollectStatusEnum.START && <CreateHomePage createBtn={renderCreateBtn} />}
        {status === CollectStatusEnum.CREATECOLLECT && <StepOneCreateSelection />}
        {status === CollectStatusEnum.SETCOLLECTCONDITION && <StepTwoGoodsFilter filterType={filterType} />}
        {status === CollectStatusEnum.MAKETAGS && <MakeTags editMakeTags={editMakeTags} />}
        {status === CollectStatusEnum.PUBLISHCOLLECT && <PublishPage />}
        {status === CollectStatusEnum.FINISH && renderFinish()}
      </div>
    </div>
  );
};

export default CreateCollect;
