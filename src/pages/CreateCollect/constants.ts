export enum ActivityTypeEnum {
  NewActivity = 'newActivity',
  OldActivity = 'oldActivity',
}

export enum FilterType {
  /** 自由选品 */
  FreeSelect = '1',
  /** 上传品集 */
  Upload = '2',
  /** 盘点货品集 */
  Inventory = '3',
}

export enum UpdateTimeTypeEnum {
  /** 每天更新（每天下午3点） */
  EVERYDAY,
  /** 时刻更新（准点时刻更新） */
  EVERYMOMENT,
  /** 小时更新（创建时间为更新时刻） */
  EVERYHOUR,
  /** 不更新（品集数据不会变化） */
  IMMUATE,
}

export enum CollectStatusEnum {
  /** 首页 */
  START,
  /** 创建品集 */
  CREATECOLLECT,
  /** 设置选品条件(选择模式) */
  SETCOLLECTCONDITION,
  /** 货品打标 */
  MAKETAGS,
  /** 发布品集 */
  PUBLISHCOLLECT,
  /** 完成发布 */
  FINISH,
}

/**
 * 货品类型
 */
export enum GoodsDataSourceTypeEnum {
  /**  LP页面 */
  LP = 'LP',
  /** POI */
  POI = 'POI',
  /** 日历房 */
  HOTEL = 'HOTEL',
  /** 淘系宝贝 */
  TRAVEL = 'TRAVEL',
  /** 酒店日历房RP */
  RP = 'RP',
  /**商家 */
  SHOP = 'SHOP',
  /**民宿 */
  BNB = 'BNB',
  /**哥伦布榜单 */
  GLB = 'GLB',
  /**机票 */
  FLIGHT = 'FLIGHT',
  /**SPU */
  SPU = 'SPU',
}

/** 活动状态 */
export enum ActivityStatus {
  /** 0-无效 */
  INVALID,
  /** 1有效 */
  VALID,
  /** 2-停用 */
  STOPPED,
  /** 3-待发布 */
  PREPUB,
  /** 4-发布中 */
  PUBLISHING,
  /** 5-发布失败 */
  PUBLISH_FAILED,
}


// 应用场景
export enum ApplyScenesEnum {
  NORMAL = 'NORMAL',
  SNAPSHOT = 'SNAPSHOT_MATCH'
}


export const APPLY_SCENES_MAP = {
  [ApplyScenesEnum.NORMAL]: '默认',
  [ApplyScenesEnum.SNAPSHOT]: '快照匹配'
}