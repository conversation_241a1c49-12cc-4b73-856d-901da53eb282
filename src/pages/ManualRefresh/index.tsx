import React, { useState } from 'react';
import { message, Form, Card, Button, Input, Select } from 'antd';
import { useSet } from '@/utils/hooks';
import './index.less';

import api from '@/api';

const { TextArea } = Input;
const { Option } = Select;

const ManualRefresh = () => {
  const [form] = Form.useForm();

  const [state, setState] = useSet({
    loading: false,
  });

  const { loading } = state;

  const onFinish = (values: any) => {
    const { dataSource, itemIds } = values;
    
    // 验证商品ID格式 - 仅支持纯数字和换行
    const itemIdPattern = /^[\d\n\r]+$/;
    if (!itemIdPattern.test(itemIds.trim())) {
      message.error('商品ID列表仅支持纯数字和换行，不支持中文符号、空格、逗号、特殊符号等');
      return;
    }

    // 处理商品ID列表，去除空行并用英文逗号拼接
    const processedItemIds = itemIds
      .trim()
      .split(/[\n\r]+/)
      .filter((id: string) => id.trim() !== '')
      .map((id: string) => id.trim())
      .join(',');

    if (!processedItemIds) {
      message.error('请输入有效的商品ID');
      return;
    }

    setState({
      loading: true,
    });

    api.activity
      .manualRefreshItemStatus({
        dataSource,
        itemIds: processedItemIds,
      })
      .then((res: any) => {
        setState({
          loading: false,
        });
        
        if (res.code === '200') {
          message.success('刷新成功');
        } else {
          message.error(res.msg || '刷新失败');
        }
      })
      .catch(() => {
        setState({
          loading: false,
        });
        message.error('请求失败，请稍后重试');
      });
  };

  return (
    <>
      <Card style={{ marginBottom: '20px' }} bordered={false}>
        <div className="flex">
          <div style={{ width: '60%' }}>
            <Form 
              labelCol={{ span: 5 }} 
              wrapperCol={{ span: 19 }} 
              onFinish={onFinish} 
              form={form}
            >
              <Form.Item 
                label="物料类型" 
                name="dataSource" 
                rules={[{ required: true, message: '请选择物料类型' }]}
              >
                <Select placeholder="请选择物料类型">
                  <Option value="TRAVEL">淘系宝贝</Option>
                </Select>
              </Form.Item>
              
              <Form.Item 
                label="商品ID列表" 
                name="itemIds" 
                rules={[{ required: true, message: '请输入商品ID列表' }]}
              >
                <TextArea
                  maxLength={50000}
                  autoSize={{ minRows: 8, maxRows: 15 }}
                  placeholder="支持多行输入，一行一个商品ID，仅支持纯数字和换行&#10;示例：&#10;237657&#10;78766&#10;77665"
                />
              </Form.Item>
              
              <Form.Item wrapperCol={{ offset: 5, span: 19 }}>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  shape="round" 
                  loading={loading}
                >
                  提交
                </Button>
              </Form.Item>
            </Form>
          </div>
          
          <div style={{ width: '40%' }} className="file_box">
            <h2>使用文档：</h2>
            <h3>功能说明：</h3>
            <p>新品手动刷新工具用于手动刷新指定商品的状态。</p>
            <p>一般用于当天新上的品，刷新状态进入星辰底池，可用于圈选。</p>
            
            <h3>使用方法：</h3>
            <div>
              1. 【物料类型】选择需要刷新的物料类型（目前仅支持淘系宝贝）<br/>
              2. 【商品ID列表】输入需要刷新的商品ID，一行一个，仅支持纯数字<br/>
              3. 【提交】点击提交按钮执行刷新操作<br/>
            </div>
            
            <h3>注意事项：</h3>
            <div style={{ color: 'red' }}>
              • 商品ID列表仅支持纯数字和换行符<br/>
              • 不支持中文符号、空格、逗号、特殊符号等<br/>
              • 每行只能输入一个商品ID<br/>
              • 刷新的商品仅支持通过Excel/Odps圈品，没有任何标签数据，标签数据需等t+1时间<br/>
            </div>
          </div>
        </div>
      </Card>
    </>
  );
};

export default ManualRefresh; 