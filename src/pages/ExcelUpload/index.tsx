import React, { useState, useEffect } from 'react';
import { message, Form, Card, Button, Input, Table, Select, Divider, Row, Popconfirm, Typography } from 'antd';
import { useSet } from '@/utils/hooks';
import { ColumnProps } from 'antd/es/table';
import ExcelUpload from '@/components/ExcelUpload';
import CommodityModal from './components/CommodityModal';
import RecordModal from './components/RecordModal';
import { get } from 'lodash';
import { downloadTemp } from '@/utils/download';

import './index.less';

import api from '@/api';

const { Option } = Select;
const { Link } = Typography;

const FilterQuery = () => {
  const [formUpload] = Form.useForm();
  const [formQuery] = Form.useForm();

  useEffect(() => {
    getTypeList();
    onFinishQuery({typeName: "u+"})
  }, []);

  const [state, setState] = useSet({
    loading: false,
    uploadBtnLoading: false,
    dataSource: [], //查询列表
    loadingType: false,
    showCreateModal: false, //弹窗
    isRecordVisible: false, //历史记录
    dataTypeList: [], //商品类型
    params: {}, //传参
    pageSize: 10,
    pageNo: 1,
    total: 0,
  });

  const {
    loading,
    dataSource,
    loadingType,
    showCreateModal,
    dataTypeList,
    params,
    pageSize,
    pageNo,
    total,
    isRecordVisible,
    uploadBtnLoading
  } = state;

  //获取商品类型列表
  const getTypeList = () => {
    setState({ loadingType: true });
    api.activity.ExcelImportToolController({ requestMapping: 'typeList' }).then((res: any) => {
      if (res.code === '200') {
        setState({ dataTypeList: get(res, 'data', []), loadingType: false });
      } else {
        message.error(res?.msg);
      }
    });
  };

  //查询商品列表
  const getList = (values: any) => {
    let data = {
      requestMapping: 'queryItem',
      pageSize,
      pageNo: 1,
      typeName: values.typeName,
      itemId: values.itemId ? values.itemId : '',
    };
    setState({
      loading: true,
    });
    api.activity.ExcelImportToolController(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data.data,
          pageNo: res.data.pageNo,
          pageSize: res.data.pageSize,
          total: res.data.total,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  const onFinishUpload = (values: any) => {
    setState({
      uploadBtnLoading: true,
    });
    api.activity.ExcelImportToolController({ requestMapping: 'upload', ...values }).then((res: any) => {
      setState({
        uploadBtnLoading: false,
      });
      if (res.code === '200') {
        message.success('上传成功');
      } else {
        message.error(res?.msg);
      }
    });
  };

  const onFinishQuery = (values: any) => {
    console.log('values--',values)
    // let formUploadData = formUpload.getFieldsValue();
    // for (const i in Object.values(formUploadData)) {
    //   if (!Object.values(formUploadData)[i]) {
    //     return message.error('请先执行上传操作');
    //   }
    // }
    getList(values);
  };

  const columnsType: ColumnProps<any>[] = [
    {
      title: '类型名称',
      dataIndex: 'typeName',
      key: 'typeName',
    },
    {
      title: '类型描述',
      dataIndex: 'typeDesc',
      key: 'typeDesc',
    },
    {
      title: '操作',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 280,
      render: (text: any, record: any, index: number) => {
        return (
          <div>
            <Button type="link" onClick={() => openEdit(record)}>
              修改
            </Button>
            <Popconfirm title="确认要删除吗？" onConfirm={() => openDelType(record)}>
              <Button type="link">删除</Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const columns: ColumnProps<any>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '商品/酒店id',
      dataIndex: 'bizId',
      key: 'bizId',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '上传时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
    },

    {
      title: '操作',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 280,
      render: (text: any, record: any, index: number) => {
        return (
          <div>
            <Popconfirm title="确认要删除吗？" onConfirm={() => openDel(record)}>
              <Button type="link">删除</Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const openEdit = (record: any) => {
    setState({
      showCreateModal: true,
      params: record,
    });
  };

  // 关闭弹窗
  const close = () => {
    setState({
      showCreateModal: false,
      params: {},
    });
  };

  // 打开历史纪录
  const openRecord = () => {
    setState({
      isRecordVisible: true,
    });
  };

  // 关闭历史纪录
  const closeRecord = () => {
    setState({
      isRecordVisible: false,
    });
  };

  // 切换页码
  const onPaginationChange = (pageNum: number, pageSize: any) => {
    const data = {
      requestMapping: 'queryItem',
      pageNo: pageNum,
      pageSize,
      typeName: formQuery.getFieldsValue().typeName,
      itemId: formQuery.getFieldsValue().itemId ? formQuery.getFieldsValue().itemId : '',
    };
    setState({
      loading: true,
    });
    api.activity.ExcelImportToolController(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data.data,
          pageNo: res.data.pageNo,
          pageSize: res.data.pageSize,
          total: res.data.total,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  // 删除商品
  const openDel = (record: any) => {
    api.activity
      .ExcelImportToolController({ requestMapping: 'delItem', typeName: record.type, itemId: record.bizId })
      .then((res: any) => {
        if (res && res.code === '200') {
          message.success(res?.msg || '删除成功');
          getList(formQuery.getFieldsValue());
        } else {
          message.error(res?.msg || '删除失败');
        }
      });
  };

  // 删除商品类型
  const openDelType = (record: any) => {
    api.activity
      .ExcelImportToolController({ requestMapping: 'typeDel', typeName: record.typeName, typeDesc: record.typeDesc })
      .then((res: any) => {
        if (res && res.code === '200') {
          message.success(res?.msg || '删除成功');
          getTypeList();
        } else {
          message.error(res?.msg || '删除失败');
        }
      });
  };

  return (
    <>
      <Card style={{ marginBottom: '20px' }} bordered={false}>
        <div className="flex">
          <div style={{ width: '60%' }}>
            <h3>已有商品类型</h3>
            <Row justify="end" style={{ marginBottom: '20px' }}>
              <Button
                type="primary"
                onClick={() => {
                  setState({ showCreateModal: true });
                }}
              >
                添加
              </Button>
            </Row>
            <Table
              scroll={{ y: 400 }}
              loading={loadingType}
              dataSource={dataTypeList}
              columns={columnsType}
              rowKey={(record) => record.typeName}
              pagination={false}
            />
          </div>
          <div style={{ width: '40%' }} className="file_box">
            <h2>使用文档：</h2>
            <div style={{color:'red',marginBottom:'10px'}}>注意：不同用途请区分开类型，不要乱用别人创建的类型，后面这里会加权限控制，只有创建上传类型的人可以给该类型上传文件。 一次性用途可以用时间或者标识开，比如 长弘-0708</div>
            <h3>上传文档：</h3>
            <div>
            1. 【类型】创建商品类型 (如果有则不用创建)<br/>
            2. 【上传】下载上传模板 <Link onClick={downloadTemp}>excel上传模板</Link> 并按模板格式把商品id填写<br/>
            3. 【上传】选择商品类型 并且 上传Excel<br/>
            4. 【查询】抽样查询校验商品上传结果<br/>
            </div>
            <h3 style={{marginTop:'10px'}}>离线表文档：</h3>
            <p>
              上传后会产出odps小时表(h+1)（未去重）：
              <a
                href="https://dmc.dw.alibaba-inc.com/dm/table/odps.trip_vacation.tripgalaxy_excel_upload_hh/detail/col"
                target={'_blank'}
              >
                odps.trip_vacation.tripgalaxy_excel_upload_hh
              </a>
            </p>
            <p>
              上传后会产出odps天表(T+1)（未去重）：
              <a
                href="https://dmc.dw.alibaba-inc.com/dm/table/odps.trip_vacation.s_excel_upload_trip_galaxy_app/detail/col"
                target={'_blank'}
              >
                odps.trip_vacation.s_excel_upload_trip_galaxy_app
              </a>
            </p>
            <div>
              离线表使用案例：(提示: 离线表字段 type 对应该工具的 type值)
              <div style={{width:"100%",height:'176px',border:'1px solid #d4c7c7',borderRadius:'14px', paddingLeft:'32px', paddingTop:'18px',background:'#f3f2f2',wordBreak:'break-word'}}>
                <div>select biz_id from </div>
                <div>trip_vacation.s_excel_upload_trip_galaxy_app</div>
                <div>where ds = MAX_PT('trip_vacation.s_excel_upload_trip_galaxy_app')</div>
                <div>and type = 'u+'</div>
                <div>group by biz_id;</div>
              </div>
            </div>
            <h3 style={{marginTop:'10px'}}>操作记录查看：</h3>
            <p>
              <Button
                type="primary"
                onClick={() => {
                  openRecord();
                }}
              >
                查看历史记录
              </Button>
            </p>
          </div>
        </div>
      </Card>
      <Card style={{ marginBottom: '20px' }} bordered={false}>
        <h3>上传</h3>
        <Form labelCol={{ span: 5 }} wrapperCol={{ span: 14 }} onFinish={onFinishUpload} form={formUpload}>
          <Form.Item label="上传商品类型" name="typeName" rules={[{ required: true, message: '请输入上传商品类型' }]}>
            <Select>
              {dataTypeList &&
                dataTypeList.length > 0 &&
                dataTypeList.map((item: any) => {
                  return (
                    <Option key={item.typeName} value={item.typeName} label={item.typeName}>
                      {item.typeName}
                    </Option>
                  );
                })}
            </Select>
          </Form.Item>
          <Form.Item label="上传excel" name="uploadUrl" rules={[{ required: true, message: '请上传excel' }]}>
            <ExcelUpload />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 12, span: 16 }}>
            <Button type="primary" htmlType="submit" shape="round" loading={uploadBtnLoading}>
              上传
            </Button>
          </Form.Item>
        </Form>
      </Card>
      <Card>
        <h3>查询</h3>
        <Form labelCol={{ span: 5 }} wrapperCol={{ span: 14 }} onFinish={onFinishQuery} form={formQuery}>
          <Form.Item label="商品类型" name="typeName" rules={[{ required: true, message: '请选择上传商品类型' }]} initialValue={'u+'}>
            <Select>
              {dataTypeList &&
                dataTypeList.length > 0 &&
                dataTypeList.map((item: any) => {
                  return (
                    <Option key={item.typeName} value={item.typeName} label={item.typeName}>
                      {item.typeName}
                    </Option>
                  );
                })}
            </Select>
          </Form.Item>
          <Form.Item label="商品ID" name="itemId" rules={[{ required: false, message: '请输入商品ID' }]}>
            <Input placeholder="请输入商品ID" />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 12, span: 16 }}>
            <Button type="primary" htmlType="submit" shape="round">
              查询
            </Button>
          </Form.Item>
        </Form>
        <p>查看结果</p>
        <Table
          scroll={{ y: 500 }}
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey={(record) => record.itemId}
          pagination={{
            pageSize: pageSize,
            current: pageNo,
            total: total,
            onChange: (num, pageSize) => onPaginationChange(num, pageSize),
          }}
          bordered
        />
      </Card>
      {showCreateModal && (
        <CommodityModal close={close} showCreateModal={showCreateModal} params={params} getTypeList={getTypeList} />
      )}

      {isRecordVisible ? <RecordModal isRecordVisible={isRecordVisible} closeRecord={closeRecord} /> : null}
    </>
  );
};

export default FilterQuery;
