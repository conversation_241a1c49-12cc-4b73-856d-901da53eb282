import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Card, Table, Modal, Popover, Button, message } from 'antd';
import api from '@/api';


const RecordModal = (props: any) => {
  const [state, setState] = useSet({
    pageSize: 10,
    pageNo: 1,
    total: 0,
    loading: false,
    dataSource: [],
  });
  const { isRecordVisible, closeRecord, ids } = props;
  const { loading, pageSize, pageNo, total, dataSource } = state;


  useEffect(()=> {
    let data:any = {
      operation:"ExcelUploadTool",
      pageNo,
      pageSize,
    }
    setState({
      loading: true,
    });
    api.activity.ExcelImportToolOpRecord(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data.data,
          pageNo: res.data.pageNo,
          pageSize: res.data.pageSize,
          total: res.data.total,
        });
      } else {
        message.error(res?.msg)
      }
    });
  },[])

  const handleCancel = () => {
    closeRecord();
  };

  const columns = [{
    title: '时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'nickName',
    key: 'nickName',
    align: 'center',
    render: (text: any, record: any, index: number) => {
      if (record?.empId) {
        return record?.empId
      } else if (record?.nickName) {
        return record?.nickName
      } else {
        return '暂无'
      }
    },
  },{
    title: '商品类型',
    dataIndex: 'actId',
    key: 'actId',
    align: 'center',
  },{
    title: '操作',
    dataIndex: 'data',
    key: 'data',
    width: 400,
  }];

  // 切换页码
  const onPaginationChange = (pageNum: number, pageSize: any) => {
    let data:any = {
      operation:"ExcelUploadTool",
      pageNo: pageNum,
      pageSize,
    }
    setState({
      loading: true,
    });
    api.activity.ExcelImportToolOpRecord(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data.data,
          pageNo: res.data.pageNo,
          pageSize: res.data.pageSize,
          total: res.data.total,
        });
      } else {
        message.error(res?.msg)
      }
    });
  };

  return (
    <Modal title="历史记录" visible={isRecordVisible} onCancel={handleCancel} width="80vw" footer={null}>
      <Table
        // scroll={{ x: 1500 }}
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        rowKey={(record) => record.id}
        pagination={{
          pageSize: pageSize,
          current: pageNo,
          total: total,
          onChange: (num, pageSize) => onPaginationChange(num, pageSize),
        }}
      />
    </Modal>
  );
};

export default RecordModal;
