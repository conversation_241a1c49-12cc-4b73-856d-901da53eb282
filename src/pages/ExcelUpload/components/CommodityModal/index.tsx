import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Modal, message, Form, Select, Cascader, Input, Row, Col } from 'antd';
import { get } from 'lodash';

import api from '@/api';

const { TextArea } = Input;

const CommodityModal = (props: any) => {
  const { showCreateModal, close, params, getTypeList} = props;

  const [form] = Form.useForm();

  useEffect(() => {
    if(JSON.stringify(params) !== '{}') {
      form.setFieldsValue({
        typeName:params?.typeName,
        typeDesc:params?.typeDesc
      })
    }
  },[params])

  const createFinish = (formData: any) => {
    if (JSON.stringify(params) === '{}') {
      //新建
      api.activity.ExcelImportToolController({requestMapping:'typeAdd', ...formData}).then((res:any) => {
        if(res.code === '200') {
          message.success(res.msg)
          getTypeList()
          close()
        } else {
          message.error(res.msg)
        }
      })
    } else {
      //编辑
      let oldFormData = {
        oldTypeName:params?.typeName,
        oldTypeDesc:params?.typeDesc
      }
      api.activity.ExcelImportToolController({requestMapping:'typeUpdate', ...formData, ...oldFormData}).then((res:any) => {
        if(res.code === '200') {
          message.success(res.msg)
          getTypeList()
          close()
        } else {
          message.error(res.msg)
        }
      })
    }
  };

  const createOk = () => {
    form.submit();
  };

  const createCancel = () => {
    close();
    form.resetFields();
  };

  return (
    <Modal title={JSON.stringify(params) !== '{}' ? '编辑商品类型' :'新增商品类型'} visible={showCreateModal} width={'50vw'} onOk={createOk} onCancel={createCancel}>
      <Form form={form} onFinish={createFinish} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <Form.Item
          label="类型名称"
          name="typeName"
          rules={[{ required: true, message: '请输入类型名称' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="类型描述"
          name="typeDesc"
          rules={[{ required: true, message: '请输入类型描述' }]}
        >
          <TextArea />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CommodityModal;
