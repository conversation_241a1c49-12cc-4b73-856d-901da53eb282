import { Link } from 'umi';
import { useState, useEffect } from 'react';
import './index.less';
import api from '@/api';

const Welcome = ({ onClick }: { onClick: () => void }) => {
  const [notice, setNotice] = useState<any>('');
  const [start, setStart] = useState<number>(0);
  const [end, setEnd] = useState<number>(0);
  const [time, setTime] = useState<number>(0);
  const [isBtn, setIsBtn] = useState<boolean>(true);

  useEffect(() => {
    api.activity.homePageNotice().then((res: any) => {
      if (res.code === '200' && res.data && res.data.content && res.data.start && res.data.end && res.data.time) {
        setNotice(decodeURIComponent(res.data.content));
        setStart(res.data.start);
        setEnd(res.data.end);
        setTime(res.data.time);
      }
    });
  }, []);

  // useEffect(() => {
  //   let timer:any
  //   if (time) {
  //     timer = setTimeout(function () {
  //       setIsBtn(true)
  //     }, time * 1000);
  //   }
  //   return () => {
  //     clearTimeout(timer);
  //   };
  // },[time])

  const isTime = () => {
    const nowTime = new Date().valueOf();
    // if (nowTime >= start && nowTime <= end) {
    //   return true;
    // }
    return false;
  };

  return (
    <div className="welcome">
      <div className="welcome-top"></div>
      <div className="btn-container">
        {isBtn ? (
          <Link className="open-btn" to="/manageCollect" onClick={onClick}>
            立即开启
          </Link>
        ) : null}
        {isTime() ? (
          <div className="welcome-notice">
            <div dangerouslySetInnerHTML={{ __html: notice }}></div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default Welcome;
