.welcome {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  .welcome-top {
    width: 100vw;
    height: 31vw;
    background-image: url('homepage.jpg');
    background-repeat: no-repeat;
    background-size: contain;
  }
  .welcome-main{
    position: relative;
    
    
  }
  
  .btn-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 8vw;
    position: relative;
    .open-btn {
      width: 13vw;
      color: #fff;
      font-size: 2vw;
      line-height: 5vw;
      text-align: center;
      background-image: linear-gradient(to right, rgb(4, 34, 63) 0%, rgb(16, 84, 159) 100%);
      border-radius: 2.5vw;
      box-shadow: 5px 5px 14px -5px #8b919b;
    }
    .welcome-notice{
      position: absolute;
      width: 30vw;
      height: 17vw;
      overflow-y: auto;
      border-radius: 1vw;
      // border: 1px solid #8b919b;
      right: 3vw;
      top: 2vw;
      background-color: rgba(161, 159, 159, 0.1);
     
    }
  }
  
}
