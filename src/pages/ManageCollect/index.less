.manage-main{
  .manage-main-top{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
}

.filter-card {
  margin-bottom: 16px;
}

.filter-form {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  .filter-form-content {
    flex: 1;

    .filter-form-row {
      margin-bottom: 16px;
      display: flex;

      .filter-form-item {
        flex: 1;
        margin-right: 24px;
        margin-bottom: 0;

        &:last-child {
          margin-right: 0;
        }

        :global {
          .ant-form-item-label {
            width: 100px;
            text-align: right;
          }
          .ant-form-item-control {
            flex: 1;
            margin-left: 8px;
          }
        }
      }
    }
  }

  .filter-buttons {
    display: flex;
    gap: 8px;
    margin-left: 16px;
  }
}