import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Modal, message, Form, Select, Cascader, Input, Radio } from 'antd';
import { get } from 'lodash';
import { useGlobal } from '@ali/use-global';

import SearchUser from '@/components/UserCollectSearch';

import api from '@/api';

const { TextArea } = Input;

const CommodityModal = (props: any) => {
  const [state, setState] = useSet({
    createOwner:'',
    confirmLoading: false
  });
  const [store] = useGlobal();
  const { currentUser } = store;

  const [form] = Form.useForm();

  const { createSelectModal, close, getData, createSelectType, createSelectFormData, pageNo, confirmLoading } = props;
  const { createOwner } = state

  useEffect(() => {
    selectOwner();
  }, [currentUser.empId]);

  useEffect(() => {
    if (createSelectModal && createSelectType === 'edit') {
      form.setFieldsValue({
        activityTitle: createSelectFormData.activityTitle,
        activityOwner: createSelectFormData.activityOwner.split(','),
        subTitle: createSelectFormData?.subTitle,
      })
    }
  },[createSelectModal, createSelectFormData])

  const createFinish = (formData: any) => {
    let data = {
      activityTitle: formData.activityTitle,
      activityOwner: formData.activityOwner.toString(),
      activityIndex: 0,
    };

    if (createSelectType === 'edit') {
      data.id = createSelectFormData.id
    }

    if (formData?.subTitle) {
      data.subTitle = formData?.subTitle
    }

    setState({
      confirmLoading: true
    })

    api.activity.save(data).then((res: any) => {
      setState({
        confirmLoading: false
      })
      close();
      if (res.code === '200') {
        message.success(res.msg);
        getData(pageNo);
      } else {
        message.error(res.msg);
      }
    });
  };

  const createOk = () => {
    form.submit();
  };

  const createCancel = () => {
    close();
    form.resetFields();
  };

  // 由于花名查找nick显示不同如无花名-xxx显示xxx，所以需要映射创建人
  const selectOwner = () => {
    fetch(`/common/GetUserInfo.do?key=${currentUser.empId}&mock=a`)
      .then((response: any) => response.json())
      .then((res) => {
        if (res && res.code === '200') {
          setState({
            createOwner:`${get(res,'data[0].nick','')}[${get(res,'data[0].empId','')}]`
          })
        }
      });
  };

  return (
    <Modal title={createSelectType === 'add' ? "新建选品集" : "编辑选品集"} visible={createSelectModal} width={'50vw'} onOk={createOk} onCancel={createCancel} confirmLoading={confirmLoading}>
      <Form form={form} onFinish={createFinish} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <Form.Item label="选品集名称" name="activityTitle" rules={[{ required: true, message: '请输入类型名称' }]}>
          <Input />
        </Form.Item>
        <Form.Item
          label="管理员"
          name="activityOwner"
          rules={[{ required: true, message: '请选择管理员' }]}
          initialValue={[createOwner]}
        >
          <SearchUser mode="multiple" />
        </Form.Item>
        <Form.Item label="备注" name="subTitle">
          <TextArea placeholder="请输入备注" showCount maxLength={40} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CommodityModal;
