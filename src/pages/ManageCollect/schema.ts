export const formSchema = {
  type: 'object',
  properties: {
    activityTitle: {
      title: '活动名称',
      type: 'string',
      'ui:width': '100%',
      default: null
    },
    activityOwner: {
      title: '成员',
      type: 'array',
      'ui:width': '100%',
      widget: 'searchUser',
      "ui:options": {
        mode: "multiple",
      }
    }
  },
  'ui:labelWidth': 120,
  required: ['activityTitle', 'activityOwner'],
}