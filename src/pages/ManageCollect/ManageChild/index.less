.manageChildMain{
  .manageChildTop{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    .manageChildBreadcrumb{
      cursor: pointer;
    }
    .manageChildTopRight{
      .manageChildBtnleft{
        margin-left: 10px;
      }
    }
  }
}

.manageOperation{
  :global {
    .ant-btn{
      padding: 0;
    }
  }
}

.filterCard {
  margin-bottom: 16px;
}

.filterForm {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  
  .filterFormContent {
    flex: 1;
    
    .filterFormRow {
      margin-bottom: 16px;
      display: flex;
      
      .filterFormItem {
        flex: 1;
        margin-right: 24px;
        margin-bottom: 0;
        
        &:last-child {
          margin-right: 0;
        }
        
        :global {
          .ant-form-item-label {
            width: 100px;
            text-align: right;
          }
          .ant-form-item-control {
            flex: 1;
            margin-left: 8px;
          }
        }
      }
    }
  }
  
  .filterButtons {
    display: flex;
    gap: 8px;
    margin-left: 16px;
  }
}

.listHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .headerButtons {
    display: flex;
    gap: 8px;
  }
}

.askTextContent {
  .askTextBlock {
    display: block;
    margin-bottom: 5px;
  }
  
  .askTextIndent {
    display: block;
    margin-left: 15px;
  }
  
  .askTextIndentBottom {
    display: block;
    margin-left: 15px;
    margin-bottom: 5px;
  }
  
  .askTextError {
    color: red;
  }
  
  .askTextLink {
    color: #008dff;
  }
  
  .askTextBold {
    font-weight: bold;
    font-size: 13px;
  }
  
  .askTextBlock2 {
    display: block;
  }
}

.popoverContent {
  max-width: 300px;
  word-break: break-word;
}