import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Modal, Row, Col, Tag, message } from 'antd';
import { cloneDeep } from 'lodash';
import api from '@/api';

const BatchExportModal = (props: any) => {
  const [state, setState] = useSet({
    btnLoading: false,
    tagList: [],
  });
  const { btnLoading, tagList } = state;
  const { batchExportVisible, selectedRowKeys, onReload, close, dataSource } = props;

  useEffect(() => {
    if (!batchExportVisible) return;
    let temp:any = []
    selectedRowKeys &&
    selectedRowKeys.length > 0 &&
    selectedRowKeys.forEach((ele: any) => {
      let arr = dataSource.find((item: any) => item?.id === ele);
      if (arr?.status === 1) {
        temp.push(arr)
      }
    });
    if (temp && temp.length > 0) {
      setState({
        tagList: temp
      })
    }
  }, [batchExportVisible]);

  const handleOk = () => {
    if(tagList && tagList.length === 0) {
      return message.error('暂无选择的活动')
    }
    let activityIds = tagList.map((ele:any) => ele?.id).join(',')
    let sumItemCount = 0
    tagList.forEach((ele:any) => {
      if (ele?.itemCount) {
        sumItemCount += ele.itemCount
      }
    })
    const data = {
      activityIds: activityIds,
      itemCount: sumItemCount,
      type: 'batch'
    };

    api.activity.exportItem(data).then((res: any) => {
      if (res.code === '200' && res.data) {
        message.success(res.data);
      } else {
        message.error(res.msg);
      }
      close()
    });
  }

  const handleCancel = () => {
    setState({
      tagList: []
    })
    close()
  }

  return (
    <Modal
      visible={batchExportVisible}
      title="批量导出"
      confirmLoading={btnLoading}
      
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <Row style={{ flexDirection: 'column' }}>
        <Col style={{ fontSize: '15px', color: 'red' }}>只有状态为【已完成】的选品池才能导出</Col>
        <Col style={{ fontSize: '15px' }}>已选活动ID：</Col>
        <Col style={{ padding: '10px 0' }}>
          {tagList && tagList.length > 0 ? (
            tagList.map((item: any) => <Tag color="orange">{item?.id}</Tag>)
          ) : (
            <div style={{ textAlign: 'center' }}>暂无选择的活动</div>
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default BatchExportModal;
