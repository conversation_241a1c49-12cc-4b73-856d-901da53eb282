import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import FormRender, { useForm } from 'form-render';
import { formSchema } from './schema';
import SearchUser from '@/components/UserCollectSearch';
import ManageChildSource from './components/ManageChildSource';
import ExpirationTime from './components/ExpirationTime';
import ChangWayCheck from './components/ChangWayCheck';
import ICTagDisplay from './components/ICTagDisplay';
import { history } from 'umi';
import { Card, Breadcrumb, Button, message } from 'antd';

import api from '@/api';

import './index.less';

const ManageChildEdit = (props: any) => {
  const [state, setState] = useSet({
    formData: [],
  });
  const { location } = props;
  const { formData } = state;
  const { id, activityParentTitle, activityChildTitle, isOwner, parentId } = location.query;
  const form = useForm();

  useEffect(() => {
    const data = {
      id,
      fetch: false,
    };
    api.activity.getActivity(data).then((res: any) => {
      if (res.code === '200' && res.data && res.data.activity) {
        if (res.data.activity.activityOwner) {
          res.data.activity.activityOwner = res.data.activity.activityOwner.split(',');
        }
        setState({
          formData: res.data.activity,
        });
        // const changWayRadio = {
        //   changWay: res.data.activity.changWay,
        //   changeTime: res.data.activity.changeTime
        // }

        // 设置IC标数据
        const formValues = {
          ...res.data.activity,
          icTags: res.data.activity.tags // 将tags字段映射到icTags
        };

        form.setValues(formValues);
        formSchema.properties.dataSource.disabled = true;
        formSchema.properties.hasTag.disabled = true;
      } else {
        message.error(res.msg);
      }
    });
  }, []);

  const onFinish = (formData: any, validation: any, remoteResult: any) => {
    if (validation.length) return;
    const data: any = {
      status: formData.status,
      activityTitle: formData.activityTitle,
      expireTime: formData.expireTime,
      id: formData.id,
      parentId: formData.parentId,
      gmtCreate: formData.gmtCreate,
      gmtModified: formData.gmtModified,
      activityOwner: formData.activityOwner,
      dataSource: formData.dataSource,
      hasTag: formData.hasTag,
      deleteTagWay: formData.deleteTagWay,
      updateType: formData.updateType,
      applyScenes: formData.applyScenes,
      // changeTime: formData.changWayRadio.changeTime,
      activityParentTitle,
      checkDisabled: true,
    };

    api.activity.save(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        if (window.location.href.includes('isFrame=true')) {
          history.push(
            `/manageCollect/manageChild?activityTitle=${activityParentTitle}&id=${parentId}&isChild=${1}&isOwner=${isOwner}&isFrame=true`,
          );
        } else {
          history.push(
            `/manageCollect/manageChild?activityTitle=${activityParentTitle}&id=${parentId}&isChild=${1}&isOwner=${isOwner}`,
          );
        }
      } else {
        message.error(res.msg);
      }
    });
  };

  const onOk = () => {
    form.submit();
  };

  return (
    <Card className="manage-child-main">
      <div className="manage-child-top">
        <Breadcrumb separator=">">
          <Breadcrumb.Item>
            {!window.location.href.includes('isFrame=true') && (
              <span
                className="manage-child-breadcrumb"
                onClick={() => {
                  isOwner === 'true' ? history.push('/manageCollect') : history.push(`/manageCollect?isOther=${true}`);
                }}
              >
                主活动列表
              </span>
            )}
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <span
              className="manage-child-breadcrumb"
              onClick={() => {
                if (window.location.href.includes('isFrame=true')) {
                  history.push(
                    `/manageCollect/manageChild?activityTitle=${activityParentTitle}&id=${parentId}&isChild=${1}&isOwner=${isOwner}&isFrame=true`,
                  );
                } else {
                  history.push(
                    `/manageCollect/manageChild?activityTitle=${activityParentTitle}&id=${parentId}&isChild=${1}&isOwner=${isOwner}`,
                  );
                }
              }}
            >
              {activityParentTitle}
            </span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{activityChildTitle ? activityChildTitle : '创建子活动'}</Breadcrumb.Item>
        </Breadcrumb>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', padding: '0 20%' }}>
        <FormRender
          form={form}
          schema={formSchema}
          onFinish={onFinish}
          showValidate={false}
          displayType="row"
          labelWidth={120}
          widgets={{
            searchUser: SearchUser,
            manageChildSource: ManageChildSource,
            expirationTime: ExpirationTime,
            changWayCheck: ChangWayCheck,
            icTagDisplay: (props: any) => (
              <ICTagDisplay
                {...props}
                activityId={formData.id}
                activityOwner={formData.activityOwner?.join?.(',') || formData.activityOwner}
              />
            ),
          }}
        />
          <Button type="primary" onClick={onOk}>
            保存
          </Button>
      </div>
    </Card>
  );
};

export default ManageChildEdit;
