import React, { useState, useEffect } from 'react';
import { Tag, Spin } from 'antd';
import api from '@/api';

interface ICTagDisplayProps {
  value?: string; // 当前选择的IC标ID，多个用逗号分隔
  activityId?: number; // 活动ID
  activityOwner?: string; // 活动所有者
  disabled?: boolean; // 是否禁用
}

const ICTagDisplay: React.FC<ICTagDisplayProps> = ({ 
  value, 
  activityId, 
  activityOwner,
  disabled = true 
}) => {
  const [tagList, setTagList] = useState<{ label: string; value: number }[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTags, setSelectedTags] = useState<{ label: string; value: number }[]>([]);

  // 获取可用的IC标列表
  useEffect(() => {
    if (activityId && activityOwner) {
      setLoading(true);
      api.activity
        .tagList({
          activityId: activityId,
          owner: activityOwner
        })
        .then((res: any) => {
          if (res.code === '200' && res.msg === '操作成功') {
            const tags = res.data.map((e: any) => ({
              label: e.tagName,
              value: e.id,
            }));
            setTagList(tags);
            
            // 根据value设置选中的标签
            if (value) {
              const selectedIds = value.split(',').map(id => parseInt(id.trim()));
              const selected = tags.filter((tag: any) => selectedIds.includes(tag.value));
              setSelectedTags(selected);
            }
          }
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [activityId, activityOwner, value]);

  if (loading) {
    return <Spin size="small" />;
  }

  if (!value || selectedTags.length === 0) {
    return <span style={{ color: '#999' }}>未选择IC标</span>;
  }

  return (
    <div>
      {selectedTags.map((tag) => (
        <Tag key={tag.value} color="blue" style={{ marginBottom: 4 }}>
          {tag.label}
        </Tag>
      ))}
      <div style={{ color: '#999', fontSize: '12px', marginTop: 4 }}>
        系统自动为品池内物料打IC标，品池过期自动去标
      </div>
    </div>
  );
};

export default ICTagDisplay;
