import React, { useState, useEffect } from 'react';
import { Select } from 'antd';
import _ from 'lodash';
const { Option } = Select;
const ManageChildSource = (props: any) => {
  let { value = undefined, onChange, name, mode = '', options = {}, disabled, addons } = props;
  mode = mode || options.mode || ''; // 兼容 FormRender，一般组件都是props传递下来，FR只能通过schema的ui:options传递下来
  if (mode === 'multiple' && !value) {
    value = [];
  } else if (!value) {
    value = undefined;
  }


  const handleChange = (value: any) => {
    // 为了兼容 FormRender，所以需要有下面的 name 判断，一般的组件封装直接通过回调函数把value带回去就行了，这里的onChange(name, value)主要是为了保证FR知道自定义组件的key
    typeof onChange === 'function' && name ? onChange(name, value) : onChange(value);
  };

  return (
    <div style={{display:'flex',alignItems:"center",width:"100%"}}>
      <Select
        value={value}
        allowClear
        mode={mode}
        disabled={disabled}
        placeholder='请选择数据源'
        showSearch
        getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
        // @ts-ignore
        filterOption={false}
        // onSearch={fetchUser}
        onChange={handleChange}
        style={{ width: '74%',marginRight:"10px" }}
      >
        <Option value="TRAVEL">淘系宝贝</Option>
        <Option value="POI">POI</Option>
        <Option value="HOTEL">日历房</Option>
        <Option value="SHOP">商家</Option>
        <Option value="BNB">民宿</Option>
        <Option value="GLB">哥伦布榜单</Option>
        <Option value="SPU">SPU</Option>
      </Select>
      <span style={{color:'red',whiteSpace: 'nowrap'}}>子活动生成后不可更改</span>
    </div>
  );
};

export default ManageChildSource;
