import React, { useState, useEffect } from 'react';
import { Radio, TimePicker, Select } from 'antd';
import moment from 'moment';
import _ from 'lodash';

const { Option } = Select;

const ChangWayCheck = (props: any) => {
  let { value = undefined, onChange, name, mode = '', options = {}, disabled, addons } = props;
  mode = mode || options.mode || ''; // 兼容 FormRender，一般组件都是props传递下来，FR只能通过schema的ui:options传递下来

  const handleChange = (value: any) => {
    // 为了兼容 FormRender，所以需要有下面的 name 判断，一般的组件封装直接通过回调函数把value带回去就行了，这里的onChange(name, value)主要是为了保证FR知道自定义组件的key
    typeof onChange === 'function' && name ? onChange(name, value) : onChange(value);
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
      <Select onChange={handleChange} style={{width:'100%'}} value={value}>
        <Option value={0}>每天更新（每天下午3点）</Option>
        {/* <Option value={1}>时刻更新（准点时刻更新）</Option>
        <Option value={2}>小时更新（创建时间为更新时刻）</Option> */}
        <Option value={3}>不更新（品集数据不会变化）</Option>
      </Select>
    </div>
  );
};

export default ChangWayCheck;
