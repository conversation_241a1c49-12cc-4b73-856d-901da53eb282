import React, { useState, useEffect } from 'react';
import { DatePicker } from 'antd';
import moment from 'moment';
import _ from 'lodash';
const ExpirationTime = (props: any) => {
  let { value = '', onChange, name, mode = '', options = {}, disabled, addons } = props;
  mode = mode || options.mode || ''; // 兼容 FormRender，一般组件都是props传递下来，FR只能通过schema的ui:options传递下来
  if (mode === 'multiple' && !value) {
    value = [];
  } else if (!value) {
    value = '';
  }

  // 限定时间
  const disabledDate = (current:any) => {
    return current < moment().startOf('day') || current > moment() + 90 * 24 * 60 * 60 * 1000;
  };

  const [data, setData] = useState<any[]>([]);

  const handleChange = (value: any) => {
    value = value && moment(value).format(
      'YYYY-MM-DD HH:mm:ss'
    )
    // 为了兼容 FormRender，所以需要有下面的 name 判断，一般的组件封装直接通过回调函数把value带回去就行了，这里的onChange(name, value)主要是为了保证FR知道自定义组件的key
    typeof onChange === 'function' && name ? onChange(name, value) : onChange(value);
  };

  return (
    <>
      <DatePicker style={{width:'100%'}} showTime onChange={handleChange} placeholder="请选择过期时间" disabledDate={disabledDate} format="YYYY-MM-DD HH:mm:ss" value={moment(value,'YYYY-MM-DD HH:mm:ss')}/>
    </>
  );
};

export default ExpirationTime;
