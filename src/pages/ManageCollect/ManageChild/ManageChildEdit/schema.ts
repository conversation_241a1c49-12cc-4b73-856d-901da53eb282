import { ApplyScenesEnum, APPLY_SCENES_MAP } from '@/pages/CreateCollect/constants';

export const formSchema = {
  type: 'object',
  properties: {
    activityTitle: {
      title: '活动名',
      type: 'string',
      'ui:width': '100%',
      'ui:options': {
        placeholder: '请输入活动名',
      },
    },
    activityOwner: {
      title: '管理员',
      type: 'array',
      'ui:width': '100%',
      widget: 'searchUser',
      'ui:options': {
        mode: 'multiple',
      },
    },
    dataSource: {
      title: '数据源',
      type: 'string',
      'ui:width': '100%',
      widget: 'manageChildSource',
      disabled: false,
    },
    expireTime: {
      title: '过期时间',
      type: 'string',
      'ui:width': '100%',
      widget: 'expirationTime',
    },
    hasTag: {
      title: '是否IC打标',
      type: 'number',
      enum: [1, 0],
      enumNames: ['是', '否'],
      hidden: '{{formData.dataSource !== "TRAVEL"}}',
      disabled: false,
    },
    icTags: {
      title: 'IC标',
      type: 'string',
      'ui:width': '100%',
      widget: 'icTagDisplay',
      hidden: '{{formData.dataSource !== "TRAVEL" || formData.hasTag !== 1}}',
      disabled: true,
    },
    updateType: {
      title: '数据更新频率',
      type: 'number',
      widget: 'changWayCheck',
      'ui:width': '100%',
    },
    desc: {
      type: 'html',
      title: '',
      default:
        "<span style='color:red'>时刻更新和小时更新不要乱选。 选品池选品条件底层数据满足实时更新才生效，否则视为不更新<span>",
    },
    applyScenes: {
      title: '应用场景',
      type: 'string',
      default: ApplyScenesEnum.NORMAL,
      enum: Object.keys(APPLY_SCENES_MAP),
      enumNames: Object.values(APPLY_SCENES_MAP),
    },
  },
  'ui:labelWidth': 120,
  required: [
    'activityTitle',
    'activityOwner',
    'dataSource',
    'hasTag',
    'changWay',
    'expireTime',
    'updateType',
    'applyScenes',
  ],
};
