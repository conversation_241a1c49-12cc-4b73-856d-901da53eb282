import React, { useEffect, useState } from 'react';
import { Table } from 'antd';
import { useGlobal } from '@ali/use-global';

import api from '@/api';

const Jurisdiction = () => {
  const [roleData, setRoleData] = useState<any[]>([]);
  const [global] = useGlobal();

  useEffect(() => {
    api.common.getAuthInfo().then((res) => {
      // console.log('res', res);
      if (res.code === '200' && res.msg === '操作成功') {
        setRoleData(res.data);
      }
    });
  }, []);

  const columns = [
    {
      title: '权限名',
      dataIndex: 'role',
    },
    {
      title: '内容',
      dataIndex: 'desc',
    },
    {
      title: '角色',
      dataIndex: 'auth',
    },
    {
      title: '操作',
      dataIndex: 'url',
      render: (text: string) => <a href={text}>申请权限</a>,
    },
  ];

  return (
    <div>
      <Table
        columns={columns}
        dataSource={roleData}
        bordered
        rowKey="role"
        pagination={false}
        title={() => <h2>HI, {global?.currentUser?.name || ''}, 以下是星辰权限列表, 请按照需求申请:</h2>}
      />
    </div>
  );
};

export default Jurisdiction;
