export const searchSchema = {
  type: 'object',
  displayType: 'row',
  properties: {
    tagName: {
      title: '标签名称',
      type: 'string',
      width: '40%',
      "ui:options": {
        placeholder: "请输入标签名称",
      },
    },
    tagId: {
      title: '标签ID',
      type: 'string',
      width: '40%',
      "ui:options": {
        placeholder: "请输入标签ID",
      },
    },
    creator: {
      title: '创建者',
      type: 'array',
      'ui:width': '40%',
      widget: 'searchUser',
      "ui:options": {
        mode: "multiple",
      }
    },
    owner: {
      title: '使用人员',
      type: 'array',
      'ui:width': '40%',
      widget: 'searchUser',
      "ui:options": {
        mode: "multiple",
      }
    },
  },
  'ui:labelWidth': 120,
}