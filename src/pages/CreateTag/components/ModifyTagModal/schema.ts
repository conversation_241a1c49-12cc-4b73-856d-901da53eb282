export const formSchema = {
  type: 'object',
  properties: {
    tagName: {
      title: '标签名称',
      type: 'string',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请输入标签名称，限制20汉字以内",
      },
      "max": 20,
      disabled:true
    },
    tagId: {
      title: '标签ID',
      type: 'string',
      width: '100%',
      "ui:options": {
        placeholder: "请输入标签ID",
      },
      disabled:true
    },
    description: {
      title: '标签说明',
      type: 'string',
      'ui:width': '100%',
      "format": "textarea",
    },
    owner: {
      title: '可打标的人',
      type: 'array',
      'ui:width': '100%',
      widget: 'searchUser',
      "ui:options": {
        mode: "multiple",
      }
    },
  },
  'ui:labelWidth': 120,
  required: ['tagName', 'tagId', 'description', 'owner'],
}