import React, { useState, useEffect } from 'react';
import FormR<PERSON>, { useForm } from 'form-render';
import SearchUser from '@/components/UserSearch';
import { formSchema } from './schema';
import { Modal,message } from 'antd';

import api from '@/api'

const ModifyTagModal = (props: any) => {
  const { modifyModal, close, detailFormData, reloadList } = props;

  useEffect(()=> {
    if (modifyModal && detailFormData) {
      const owner = detailFormData.owner.split(',')
      form.setValues({...detailFormData,owner}); 
    }
  },[modifyModal])

  const form = useForm();

  const modifyFinish = (formData: any, validation: any, remoteResult: any) => {
    if (validation.length) return;
    if (formData && formData.owner && formData.owner.length > 0) {
      formData.owner = formData.owner.toString()
    }
    const data = {
      id: formData.id,
      description: formData.description,
      owner: formData.owner
    }
    api.tag.saveTag(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg)
        reloadList()
        close()
        form.resetFields();
      } else {
        message.error(res.msg)
      }
    })
  };

  const modifyOk = () => {
    form.submit();
  };

  const modifyCancel = () => {
    close();
    form.resetFields();
  };

  const titleName = (detailFormData: any) => {
    return 'ICTag';
  }; 

  return (
    <Modal
      title={`${titleName(detailFormData)}修改`}
      visible={modifyModal}
      width={540}
      onOk={modifyOk}
      onCancel={modifyCancel}
      okText="修改"
    >
      <FormRender
        form={form}
        schema={formSchema}
        onFinish={modifyFinish}
        showValidate={false}
        displayType="row"
        labelWidth={120}
        widgets={{
          searchUser: SearchUser,
        }}
      />
    </Modal>
  );
};

export default ModifyTagModal;
