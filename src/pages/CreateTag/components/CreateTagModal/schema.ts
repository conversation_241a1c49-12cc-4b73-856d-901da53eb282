export const formSchema = {
  type: 'object',
  properties: {
    belongTo: {
      title: '标签类型',
      type: 'number',
      enum: [0],
      enumNames: ['ICtag'],
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请选择标签类型",
      },
      widget: 'select',
      default: 0
    },
    tagName: {
      title: '标签名称',
      type: 'string',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请输入标签名称，限制20汉字以内",
      },
      "max": 20,
    },
    tagId: {
      title: '标签ID',
      type: 'string',
      width: '100%',
      "ui:options": {
        placeholder: "请输入标签ID",
      },
      rules: [
        {
          validator: (rule: any, value: any) => {
            if (!value) {
              return new Error('请输入标签ID');
            }
            if (value === '72898' || value === '92354' || value === '295809' || value === '171394' || value === '158210' || value === '148674' || value === '403330' || value ==='523458' || value === '203842' || value === '93058') {
              return new Error(`此标签为管控标，不允许创建，请更换或联系@舍离`);
            }
            return true;
          },
        },
      ],
    },
    description: {
      title: '标签说明',
      type: 'string',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请输入标签说明，限制20汉字以内",
      },
      "format": "textarea",
      "max": 20,
    },
    owner: {
      title: '使用人员',
      type: 'array',
      'ui:width': '100%',
      widget: 'searchUser',
      "ui:options": {
        mode: "multiple",
      }
    },
  },
  'ui:labelWidth': 120,
  required: ['belongTo', 'tagName', 'tagId', 'description', 'owner'],
}