import React, { useState, useEffect } from 'react';
import FormRender, { useForm } from 'form-render';
import SearchUser from '@/components/UserSearch';
import { formSchema } from './schema';
import { Modal,message } from 'antd';

import api from '@/api'

const ManageEditModal = (props: any) => {
  const { createModal,close,reloadList } = props;

  const createFinish = (formData: any, validation: any, remoteResult: any) => {
    if (validation.length) return;
    if (formData && formData.owner && formData.owner.length > 0) {
      formData.owner = formData.owner.toString()
    }
    const data = {
      ...formData
    }
    api.tag.applyTag(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg)
        reloadList()
        close()
        form.resetFields();
      } else {
        message.error(res.msg)
      }
    })
    
    
  };

  const createOk = () => {
    form.submit();
  };

  const createCancel = () => {
    close()
    form.resetFields();
  };

  const form = useForm();

  return (
    <Modal title="ICTag关联" visible={createModal} width={540} onOk={createOk} onCancel={createCancel}>
      <FormRender
        form={form}
        schema={formSchema}
        onFinish={createFinish}
        showValidate={false}
        displayType="row"
        labelWidth={120}
        widgets={{
          searchUser: SearchUser,
        }}
      />
    </Modal>
  );
};

export default ManageEditModal;
