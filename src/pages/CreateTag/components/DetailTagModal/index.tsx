import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { ColumnProps } from 'antd/es/table';
import { Modal, Form, Card, Button, Table, message } from 'antd';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const DetailTagModal = (props: any) => {
  const { detailModal, close, detailFormData } = props;
  const detailCancel = () => {
    close();
  };

  const titleName = (detailFormData: any) => {
    return 'ICTag';
  }

  return (
    <Modal title={`${titleName(detailFormData)}详情`} visible={detailModal} width={540} onCancel={detailCancel} footer={null}>
      <Form {...formItemLayout}>
        <Form.Item label="标签名">
          <span className="ant-form-text">{detailFormData && detailFormData.tagName}</span>
        </Form.Item>
        <Form.Item label="标签ID">
          <span className="ant-form-text">{detailFormData && detailFormData.tagId}</span>
        </Form.Item>
        <Form.Item label="标签描述">
          <span className="ant-form-text" style={{marginTop:'4px'}}>{detailFormData && detailFormData.description}</span>
        </Form.Item>
        <Form.Item label="可打标人">
          <span className="ant-form-text">{detailFormData && detailFormData.owner}</span>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DetailTagModal;
