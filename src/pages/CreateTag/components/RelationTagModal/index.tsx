import React, { useState, useEffect } from 'react';
import { Modal, Table } from 'antd';

const RelationTagModal = (props: any) => {
  const { relationModal, close, Relationloading , relationData } = props;

  const relationCancel = () => {
    close();
  };

  const columnsForTagActive = [
    {
      title: '活动ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: any, record: any, index: number) => {
        return <span>{record.parentId + '-' + text}</span>;
      },
    },
    {
      title: '活动名',
      dataIndex: 'activityTitle',
      key: 'activityTitle',
    },
    {
      title: '成员',
      dataIndex: 'activityOwner',
      key: 'activityOwner',
    },
  ];

  return (
    <Modal visible={relationModal} width={540} onCancel={relationCancel} footer={null}>
      <Table
        rowKey="id"
        style={{ marginTop: 20 }}
        loading={Relationloading}
        pagination={false}
        dataSource={relationData}
        columns={columnsForTagActive}
      />
    </Modal>
  );
};

export default RelationTagModal;
