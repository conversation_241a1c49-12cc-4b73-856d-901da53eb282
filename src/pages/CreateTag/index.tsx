import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import FormRender, { useForm } from 'form-render';
import SearchUser from '@/components/UserSearch';
import { ColumnProps } from 'antd/es/table';
import { Card, Button, Table, message } from 'antd';

import { createTagProps } from './interface';
import { searchSchema } from './schema';
import CreateTagModal from './components/CreateTagModal';
import DetailTagModal from './components/DetailTagModal';
import ModifyTagModal from './components/ModifyTagModal';
import RelationTagModal from './components/RelationTagModal';
import './index.less';

import api from '@/api';

const CreateTag = () => {
  const [state, setState] = useSet({
    loading: false,
    pageSize: 10,
    pageNo: 1,
    total: 0,
    formData: {}, //搜索
    isApply: false,
    createModal: false, //创建标签弹框
    detailModal: false, //查看详情弹框
    modifyModal: false, //管理权限人
    relationModal: false, //关联活动
    Relationloading: false, //关联活动loading
    relationData: [], //关联活动
    detailFormData: null, //详情内容
    dataSource: [],
  });

  const {
    loading,
    pageSize,
    pageNo,
    total,
    isApply,
    createModal,
    detailModal,
    modifyModal,
    relationModal,
    Relationloading,
    relationData,
    detailFormData,
    dataSource,
    formData,
  } = state;

  const form = useForm();

  useEffect(() => {
    getData();
  }, []);

  // 第一次请求
  const getData = () => {
    const data = {
      pageNo,
      pageSize,
    };
    setState({
      loading: true,
    });
    api.tag.tagList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  // 查看详情
  const openDetail = (record: any) => {
    setState({ detailModal: true, detailFormData: record });
  };

  // 管理权限人
  const openModify = (record: any) => {
    setState({ modifyModal: true, detailFormData: record });
  };

  // 关联活动
  const openRelation = (record: any) => {
    const data = {
      id: record.id,
    };
    setState({
      Relationloading: true,
    });
    api.tag.tagActivityList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          Relationloading: false,
          relationData: res.data,
          relationModal: true,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  // 通过审批
  const acceptTag = (record: any, index: number) => {
    const data = {
      id: record.id,
      status: 1,
      belongTo: record.belongTo,
    };
    api.tag.acceptTag(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        reloadList();
      } else {
        message.error(res.msg);
      }
    });
  };

  // 拒绝审批
  const refuseTag = (record: any, index: number) => {
    const data = {
      id: record.id,
      status: 4,
      belongTo: record.belongTo,
    };
    api.tag.refuseTag(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        reloadList();
      } else {
        message.error(res.msg);
      }
    });
  };

  // 切换页码
  const onPaginationChange = (pageNum: number,pageSize:any) => {
    const data = {
      pageNo: pageNum,
      pageSize,
      ...formData,
    };
    setState({
      loading: true,
    });
    api.tag.tagList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  const onFinish = (formData: any, validation: any, remoteResult: any) => {
    if (formData && formData.creator && formData.creator.length > 0) {
      formData.creator = formData.creator.toString();
    }
    if (formData && formData.owner && formData.owner.length > 0) {
      formData.owner = formData.owner.toString();
    }
    const data = {
      pageNo: 1,
      pageSize,
      ...formData,
    };
    setState({
      loading: true,
    });
    api.tag.tagList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
          formData: formData,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  // 关闭标签
  const close = () => {
    setState({
      createModal: false,
      detailModal: false,
      modifyModal: false,
      relationModal: false,
      relationData: null,
      detailFormData: null,
    });
  };

  // 重新加载列表
  const reloadList = () => {
    if (formData && formData.creator && formData.creator.length > 0) {
      formData.creator = formData.creator.toString();
    }
    if (formData && formData.owner && formData.owner.length > 0) {
      formData.owner = formData.owner.toString();
    }
    const data = {
      pageNo,
      pageSize,
      ...formData,
    };
    setState({
      loading: true,
    });
    api.tag.tagList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
          formData: formData,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  const columns: ColumnProps<createTagProps>[] = [
    {
      title: '标签名称',
      dataIndex: 'tagName',
      key: 'tagName',
    },
    {
      title: '标签ID',
      dataIndex: 'tagId',
      key: 'tagId',
    },
    {
      title: '标签类型',
      dataIndex: 'belongTo',
      key: 'belongTo',
      render: (text: any, record: any, index: number) => {
        return <span>ICTag</span>;
      },
    },
    {
      title: '标签状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: any, record: any, index: number) => {
        switch (text - 0) {
          case 0:
            return <span>无效</span>;
          case 1:
            return <span>审批通过</span>;
          case 2:
            return <span>审批中</span>;
          case 4:
            return <span>审批拒绝</span>;
        }
      },
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
    },
    {
      title: '操作',
      dataIndex: 'isOwner',
      key: 'isOwner',
      align: 'center',
      render: (text: any, record: any, index: number) => {
        // isOwner（0-无权限，1-有权限，2-管理员）
        let type = 'ICTag';

        if (isApply) text = 2;
        return (
          <p className="options">
            <Button type="link" onClick={() => openDetail(record)}>
              详情
            </Button>
            {(text === 1 || text === 2) && (
              <Button type="link" onClick={() => openModify(record)}>
                管理权限人
              </Button>
            )}
            {(text === 1 || text === 2) && (
              <Button type="link" onClick={() => openRelation(record)}>
                关联活动
              </Button>
            )}
            {text === 2 && record.status - 0 !== 1 && record.status - 0 !== 4 && (
              <Button type="link" onClick={() => acceptTag(record, index)}>
                审批通过
              </Button>
            )}
            {text === 2 && record.status - 0 !== 1 && record.status - 0 !== 4 && (
              <Button type="link" onClick={() => refuseTag(record, index)}>
                审批拒绝
              </Button>
            )}
          </p>
        );
      },
    },
  ];

  return (
    <>
      <Card className="create-tag-main">
        <div className="create-tag-top">
          <FormRender
            form={form}
            schema={searchSchema}
            onFinish={onFinish}
            widgets={{
              searchUser: SearchUser,
            }}
          />
          <div className="create-tag-right">
            <Button
              className="create-tag-btnleft"
              onClick={() => {
                form.submit();
              }}
            >
              查询
            </Button>
            <Button
              className="create-tag-btnleft"
              onClick={() => {
                form.resetFields();
                getData();
                setState({
                  formData: {},
                });
              }}
            >
              重置
            </Button>
            <Button
              className="create-tag-btnleft"
              type="primary"
              onClick={() => {
                setState({ createModal: true });
              }}
            >
              ICTag关联
            </Button>
          </div>
        </div>
        <Table
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey={(record) => record.id}
          pagination={{
            pageSize: pageSize,
            current: pageNo,
            total: total,
            onChange: (num,pageSize) => onPaginationChange(num,pageSize),
          }}
        />
      </Card>
      <CreateTagModal createModal={createModal} close={close} reloadList={reloadList} />
      <DetailTagModal detailModal={detailModal} detailFormData={detailFormData} close={close} />
      <ModifyTagModal modifyModal={modifyModal} detailFormData={detailFormData} close={close} reloadList={reloadList} />
      <RelationTagModal
        relationModal={relationModal}
        relationData={relationData}
        Relationloading={Relationloading}
        close={close}
      />
    </>
  );
};

export default CreateTag;
