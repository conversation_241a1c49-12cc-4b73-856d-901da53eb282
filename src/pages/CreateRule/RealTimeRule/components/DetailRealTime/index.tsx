import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { ColumnProps } from 'antd/es/table';
import { Modal, Form, Card, Button, Table, message } from 'antd';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const DetailRealTime = (props: any) => {
  const { detailModal, close, detailFormData, dataTypeList } = props;
  const detailCancel = () => {
    close();
  };

  const onImportType = (type:number) => {
    if (type === 1) {
      return '接口接入'
    } else if (type === 2) {
      return '消息接入'
    }
  }

  const updateCycleType = (text:string) => {
    if (text === "0 0 12 * * ?") {
      return '每天更新'
    } else if (text === "0 1 9-23 * * ?") {
      return '每小时更新'
    } else if (text === "never") {
      return '永不更新'
    }
  }

  const onDataSource = (text:string) => {
    let dataType;
    if (dataTypeList && Object.getOwnPropertyNames(dataTypeList).length > 0) {
      Object.keys(dataTypeList).map((item: any) => {
        if (text.toUpperCase() === dataTypeList[item]) {
          dataType = item;
        }
      });
    }
    return dataType
  }

  return (
    <Modal title='标签详情' visible={detailModal} width={540} onCancel={detailCancel} footer={null}>
      <Form {...formItemLayout}>
        <Form.Item label="接入形式">
          <span className="ant-form-text">{detailFormData && onImportType(detailFormData.importType)}</span>
        </Form.Item>
        {/* <Form.Item label="接入说明">
          <span className="ant-form-text" style={{marginTop:'4px'}}>{detailFormData && detailFormData.tagId}</span>
        </Form.Item> */}
        <Form.Item label="规则名称">
          <span className="ant-form-text">{detailFormData && detailFormData.tagNameCn}</span>
        </Form.Item>
        <Form.Item label="所属业务">
          <span className="ant-form-text">{detailFormData && detailFormData.business}</span>
        </Form.Item>
        <Form.Item label="货品类型">
          <span className="ant-form-text">{detailFormData && onDataSource(detailFormData.dataSource)}</span>
        </Form.Item>
        <Form.Item label="接口版本">
          <span className="ant-form-text">{detailFormData && detailFormData.dataInterfaceVersion}</span>
        </Form.Item>
        <Form.Item label="更新机制">
          <span className="ant-form-text">{detailFormData && updateCycleType(detailFormData.updateCycle)}</span>
        </Form.Item>
        <Form.Item label="过期时间">
          <span className="ant-form-text">{detailFormData && detailFormData.expiredTime}</span>
        </Form.Item>
        <Form.Item label="规则说明">
          <span className="ant-form-text" style={{marginTop:'4px'}}>{detailFormData && detailFormData.tips}</span>
        </Form.Item>
        <Form.Item label="对接人员">
          <span className="ant-form-text">{detailFormData && detailFormData.submitOwner}</span>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DetailRealTime;
