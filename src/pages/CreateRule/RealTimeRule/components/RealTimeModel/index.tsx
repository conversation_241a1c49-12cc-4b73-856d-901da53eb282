import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Modal, message, Form, Select, Cascader, Input, Row, Col } from 'antd';

import SearchUser from '@/components/UserSearch';
import ReaAccess from '../RealAccess';
import ExpirationTime from '@/components/ExpirationTime';
import { get } from 'lodash';
import { getVal } from '@/utils/utils';

import api from '@/api';
const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const { Option } = Select;
const { TextArea } = Input;

const RealTimeModel = (props: any) => {
  const { showCreateModal, close, reloadList, detailFormData } = props;

  const [state, setState] = useSet({
    dataTypeList: {},
    optionsData: [],
  });

  const { dataTypeList, optionsData } = state;

  const [form] = Form.useForm();

  useEffect(() => {
    api.rule.dataSource().then((res: any) => {
      if (res.code === '200') {
        setState({
          dataTypeList: res.data,
        });
      } else {
        message.error(res.msg);
      }
    });
  }, []);

  // useEffect(() => {
  //   if(detailFormData) {
  //     onBackFill(get(detailFormData, 'dataSource',''))
  //     form.setFieldsValue({
  //       importType: get(detailFormData, 'importType', '')?.toString(),
  //       mqFlag: get(detailFormData, 'mqFlag', ''),
  //       ruleInterfaceVersion: get(detailFormData, 'ruleInterfaceVersion', ''),
  //       dataInterfaceVersion: get(detailFormData, 'dataInterfaceVersion', ''),
  //       business: get(detailFormData,'business',''),
  //       tagNameCn: get(detailFormData,'tagNameCn',''),
  //       submitOwner: get(detailFormData, 'submitOwner',[]),
  //       dataSource: get(detailFormData, 'dataSource','')?.toUpperCase(),
  //       ruleType: get(detailFormData, 'ruleType',''),
  //       ruleOp: get(detailFormData, 'ruleOp',''),
  //       ruleCategory: getVal(optionsData,get(detailFormData, 'ruleCategory',''),[]),
  //       tips: get(detailFormData, 'tips',''),
  //       updateCycle: get(detailFormData, 'updateCycle',''),
  //       ds: get(detailFormData, 'ds',undefined),
  //       expiredTime: get(detailFormData, 'expiredTime',undefined),
  //       condition: get(detailFormData, 'condition', undefined),
  //     })
  //   }
  // },[detailFormData])

  const createFinish = (formData: any) => {
    formData.submitOwner = formData.submitOwner.toString();
    formData.ruleCategory = formData.ruleCategory[formData.ruleCategory.length - 1];
    if (!formData.ruleCategory) {
      message.error('请选择展示位置')
      return
    }
    const data = {
      ...formData,
    };

    api.rule.createRealtimeRuleConfig(data).then((res: any) => {
      if (res.code === '200') {
        message.success('新建成功');
        reloadList();
        close();
      } else {
        message.error(res.msg);
      }
    });
  };

  const createOk = () => {
    form.submit();
  };

  const createCancel = () => {
    close();
    form.resetFields();
  };

  const onDataType = (value: any) => {
    if (value) {
      const data = {
        dataType: value,
      };
      api.rule.getRuleLevelTree(data).then((res: any) => {
        if (res.code === '200') {
          setState({ optionsData: res.data.childNode });
        }
      });
    }
  };

  return (
    <Modal title={'新增规则'} visible={showCreateModal} width={'80vw'} onOk={createOk} onCancel={createCancel}>
      <Form form={form} onFinish={createFinish}>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Item label="接入形式" name="importType" rules={[{ required: true, message: '请选择对接方式' }]}>
              <ReaAccess></ReaAccess>
            </Form.Item>
          </Col>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.importType !== currentValues.importType}
          >
            {({ getFieldValue }) =>
              getFieldValue('importType') === '2' ? (
                <Col span={12}>
                  <Form.Item label="mq标识" name="mqFlag" rules={[{ required: true, message: '请输入mq标识' }]}>
                    <Input placeholder="请输入mq标识" />
                  </Form.Item>
                </Col>
              ) : null
            }
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.importType !== currentValues.importType}
          >
            {({ getFieldValue }) =>
              getFieldValue('importType') === '1' ? (
                <Col span={12}>
                  <Form.Item
                    label="规则树接口版本"
                    name="ruleInterfaceVersion"
                    rules={[{ required: true, message: '请输入规则树接口版本' }]}
                  >
                    <Input placeholder="请输入规则树接口版本" />
                  </Form.Item>
                </Col>
              ) : null
            }
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.importType !== currentValues.importType}
          >
            {({ getFieldValue }) =>
              getFieldValue('importType') === '1' ? (
                <Col span={12}>
                  <Form.Item
                    label="数据接口版本"
                    name="dataInterfaceVersion"
                    rules={[{ required: true, message: '请输入数据接口版本' }]}
                  >
                    <Input placeholder="请输入数据接口版本" />
                  </Form.Item>
                </Col>
              ) : null
            }
          </Form.Item>
          <Col span={12}>
            <Form.Item label="所属业务" name="business" rules={[{ required: true, message: '请输入所属业务' }]}>
              <TextArea showCount maxLength={20} autoSize={{ minRows: 1, maxRows: 1 }} placeholder="选品筛选条件名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="规则名称" name="tagNameCn" rules={[{ required: true, message: '请输入规则名称' }]}>
              <TextArea showCount maxLength={20} autoSize={{ minRows: 1, maxRows: 1 }} placeholder="选品筛选条件名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="dataSource" label="货品类型" rules={[{ required: true, message: '请选择货品类型' }]}>
              <Select onChange={onDataType} placeholder="请选择货品类型">
                {dataTypeList &&
                  Object.getOwnPropertyNames(dataTypeList).length > 0 &&
                  Object.keys(dataTypeList).map((item: any) => {
                    return (
                      <Option key={dataTypeList[item]} value={dataTypeList[item]} label={item}>
                        {item}
                      </Option>
                    );
                  })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="ruleType" label="规则样式" rules={[{ required: true, message: '请选择规则样式' }]}>
              <Select placeholder="请选择规则样式">
                <Option value="CHECKBOX">下拉框</Option>
                <Option value="INPUT">输入框</Option>
                <Option value="RADIO">单选框</Option>
                <Option value="TREE">树状图</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="ruleOp" label="规则逻辑" rules={[{ required: true, message: '请选择规则逻辑' }]}>
              <Select placeholder="请选择规则逻辑">
                <Option value="BETWEEN_AND">区间</Option>
                <Option value="EQUAL">等于</Option>
                <Option value="GT">大于</Option>
                <Option value="GTE">大于等于</Option>
                <Option value="LT">小于</Option>
                <Option value="LTE">小于等于</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="ruleCategory" label="展示位置" rules={[{ required: true }]}>
              <Cascader
                style={{ width: '100%' }}
                options={optionsData}
                placeholder="请选择展示位置"
                fieldNames={{ label: 'value', value: 'id', children: 'childNode' }}
              ></Cascader>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="规则说明" name="tips" rules={[{ required: true, message: '请输入规则说明' }]}>
              <TextArea showCount maxLength={50} placeholder="请输入规则说明" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="updateCycle" label="更新机制" rules={[{ required: true, message: '请选择更新机制' }]}>
              <Select placeholder="请选择更新机制">
                <Option value="0 0 12 * * ?">每天更新</Option>
                <Option value="0 1 9-23 * * ?">每小时更新</Option>
                <Option value="never">永不更新</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="submitOwner" label="对接人员" rules={[{ required: true, message: '请选择对接人员' }]}>
              <SearchUser mode="multiple"></SearchUser>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="分区" name="ds"  labelCol={{span:4}}>
              <Input placeholder="请输入分区（指定分区（需要传 20210101）" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="过期时间" name="expiredTime">
              <ExpirationTime></ExpirationTime>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="SQL条件" name="condition">
              <Input placeholder="请输入SQL条件" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <p style={{ fontSize: '14px', color: 'gray', paddingLeft: '6%' }}>规则长期在线，则无需配置「过期时间」</p>
      <p style={{ paddingLeft: '6%' }}>注：有疑问请联系长弘～</p>
    </Modal>
  );
};

export default RealTimeModel;
