export const formSchema = {
  type: 'object',
  properties: {
    belongTo: {
      title: '接入形式',
      type: 'string',
      'ui:width': '100%',
      widget: 'reaAccess',
    },
    tagName: {
      title: '所属业务',
      type: 'string',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请输入所属业务",
      },
      max: 15,
    },
    ruleName: {
      title: '规则名称',
      type: 'string',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "选品筛选条件名称",
      },
    },
    tagId: {
      title: '货品类型',
      type: 'string',
      width: '100%',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请选择货品类型",
      },
      enum: [
        "淘系宝贝",
        "POI",
        "日历房",
        "LP页面",
        "日历房rp"
      ],
      enumNames: [
        "淘系宝贝",
        "POI",
        "日历房",
        "LP页面",
        "日历房rp"
      ],
      widget: "select"
    },
    description: {
      title: '规则说明',
      type: 'string',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请输入规则说明",
      },
      "format": "textarea",
      "max": 50,
    },
    ruleStyle: {
      title: '规则样式',
      type: 'string',
      width: '100%',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请选择规则样式",
      },
      enum: [
        "单选下拉框",
        "树状框",
        "判断组件（是或否）",
        "区间数值输入框",
        "文本输入框",
        "日期组件（开始或结束）",
        "日期组件（开始结束）"
      ],
      enumNames: [
        "单选下拉框",
        "树状框",
        "判断组件（是或否）",
        "区间数值输入框",
        "文本输入框",
        "日期组件（开始或结束）",
        "日期组件（开始结束）"
      ],
      widget: "select"
    },
    ruleLogic: {
      title: '规则逻辑',
      type: 'string',
      width: '100%',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请选择规则逻辑",
      },
      enum: [
        "等于",
        "大于（不好喊）",
        "大于（包含）",
        "小于（包含）",
        "小于（不包含）",
        "区间值",
        "除区间值以外"
      ],
      enumNames: [
        "等于",
        "大于（不好喊）",
        "大于（包含）",
        "小于（包含）",
        "小于（不包含）",
        "区间值",
        "除区间值以外"
      ],
      widget: "select"
    },
    displayLocation: {
      title: '展示位置',
      type: 'array',
      'ui:width': '100%',
      widget: 'displayLocation',
    },
    interfaceVersion: {
      title: '接口版本',
      type: 'string',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请输入接口版本号",
      },
    },
    update: {
      title: '规则样式',
      type: 'string',
      width: '100%',
      'ui:width': '100%',
      "ui:options": {
        placeholder: "请选择规则名称",
      },
      enum: [
        "时刻更新",
        "天更新"
      ],
      enumNames: [
        "时刻更新",
        "天更新"
      ],
      widget: "select"
    },
    owner: {
      title: '对接人员',
      type: 'string',
      'ui:width': '100%',
      widget: 'searchUser',
    },
    time:{
      title: "过期日期",
      type: "string",
      'ui:width': '100%',
      widget: 'expirationTime',
    }
  },
  'ui:labelWidth': 120,
  required: ['belongTo', 'tagName', 'tagId', 'description', 'ruleStyle','ruleLogic','displayLocation','interfaceVersion','update','owner','ruleName'],
}