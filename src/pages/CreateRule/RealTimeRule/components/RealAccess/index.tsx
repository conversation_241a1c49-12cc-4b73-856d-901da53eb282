import React, { useState, useEffect } from 'react';
import { Select, Popover, Button } from 'antd';
import _ from 'lodash';
const { Option } = Select;
import { QuestionCircleOutlined } from '@ant-design/icons';
const ReaAccess = (props: any) => {
  let { value = '', onChange, name, mode = '', options = {}, disabled, addons } = props;
  mode = mode || options.mode || ''; // 兼容 FormRender，一般组件都是props传递下来，FR只能通过schema的ui:options传递下来
  if (mode === 'multiple' && !value) {
    value = [];
  } else if (!value) {
    value = '';
  }

  const [data, setData] = useState<any[]>([]);

  const content = (
    <Button type="link" onClick={()=> {window.open("https://yuque.antfin-inc.com/docs/share/d2711668-4512-41ba-ad0f-7ef5cb7715a2?#");}}>跳转到接入说明</Button>
  );

  const handleChange = (value: any) => {
    // 为了兼容 FormRender，所以需要有下面的 name 判断，一般的组件封装直接通过回调函数把value带回去就行了，这里的onChange(name, value)主要是为了保证FR知道自定义组件的key
    typeof onChange === 'function' && name ? onChange(name, value) : onChange(value);
  };

  return (
    <div style={{display:'flex',alignItems:"center",width:"100%"}}>
      <Select
        allowClear
        mode={mode}
        value={value}
        disabled={disabled}
        placeholder='请选择对接方式'
        showSearch
        getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
        // @ts-ignore
        filterOption={false}
        // onSearch={fetchUser}
        onChange={handleChange}
        style={{ width: '76%',marginRight:"10px" }}
      >
        <Option value='1'>接口接入</Option>
        <Option value='2'>消息接入</Option>
      </Select>
      <Popover content={content}>
        <div style={{cursor:'pointer'}}>
          <QuestionCircleOutlined />
          接入说明
        </div>
      </Popover>
    </div>
  );
};

export default ReaAccess;
