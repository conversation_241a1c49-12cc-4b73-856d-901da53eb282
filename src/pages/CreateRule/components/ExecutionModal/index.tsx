import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Table, Modal} from 'antd';
import api from '@/api';

interface ModalRecordProps {
  showExecutionModal: boolean;
  close: () => void;
  uuid?: number|string
  tagNameCn: string
  modalType: string
}

const ModalRecord = (props: ModalRecordProps) => {
  const [state, setState] = useSet({
    pageSize: 10,
    pageNo: 1,
    total: 0,
    loading: false,
    dataSource: [],
  });
  const { showExecutionModal, close, uuid, tagNameCn, modalType } = props;
  const { loading, pageSize, pageNo, total, dataSource } = state;


  useEffect(()=> {
    const data = {
      uuid: uuid,
      pageNo,
      pageSize,
    };
    setState({
      loading: true,
    });
    api.activity.GetTaskRecord(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      }
    });
  },[])

  const handleCancel = () => {
    close();
  };

  const columns = [{
    title: '执行时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'taskOperator',
    key: 'taskOperator',
    align: 'center',
    render: (text: any, record: any, index: number) => (
      record.taskOperator ? record.taskOperator : '暂无'
    ),
  },{
    title: <div>{modalType === 'real' ? '接口版本' : 'odps表名'}</div>,
    dataIndex: 'uuid',
    key: 'uuid',
    align: 'center',
  },{
    title: '任务状态',
    dataIndex: 'taskStatus',
    key: 'taskStatus',
    align: 'center',
  },{
    title: '备注',
    dataIndex: 'taskData',
    key: 'taskData',
    render: (text: any, record: any, index: number) => (
      <div style={{'whiteSpace':'pre-line'}}>{record?.taskData ?record?.taskData : '暂无' }</div>
    ),
  }];

  // 切换页码
  const onPaginationChange = (pageNum: number, pageSize: any) => {
    const data = {
      uuid: uuid,
      pageNo: pageNum,
      pageSize,
    };
    setState({
      loading: true,
    });
    api.activity.GetTaskRecord(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      }
    });
  };

  return (
    <Modal title={`执行记录：${tagNameCn}`} visible={showExecutionModal} onCancel={handleCancel} width="80vw" footer={null}>
      <Table
        // scroll={{ x: 1500 }}
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        rowKey={(record) => record.id}
        pagination={{
          pageSize: pageSize,
          current: pageNo,
          total: total,
          onChange: (num, pageSize) => onPaginationChange(num, pageSize),
        }}
      />
    </Modal>
  );
};

export default ModalRecord;
