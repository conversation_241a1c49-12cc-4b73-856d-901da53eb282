import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Modal, message, Form, Select, InputNumber, Input } from 'antd';

import api from '@/api';
const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const { Option } = Select;
const { TextArea } = Input;

const RealTimeModel = (props: any) => {
  const { showDelayModal, close, reloadList,ids } = props;

  const createFinish = (formData: any) => {
    let data = {
      id:ids,
      days:formData?.days
    }
    api.activity.ExtendExpireTime(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        reloadList();
        close();
      } else {
        message.error(res.msg);
      }
    });
  };

  const createOk = () => {
    form.submit();
  };

  const createCancel = () => {
    close();
    form.resetFields();
  };

  const [form] = Form.useForm();


  return (
    <Modal title="延期" visible={showDelayModal} width={540} onOk={createOk} onCancel={createCancel}>
      <Form {...layout} form={form} onFinish={createFinish}>
        <Form.Item label="延期天数" name="days" rules={[{ required: true, message: '请输入延期天数' }]}>
          <InputNumber min={1} style={{'width':'100%'}}/>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default RealTimeModel;
