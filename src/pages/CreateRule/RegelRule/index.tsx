import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import FormRender, { useForm } from 'form-render';
import SearchUser from '@/components/UserSearch';
import { ColumnProps } from 'antd/es/table';
import { Card, Button, Table, message, Popover } from 'antd';
import { createTagProps } from './interface';

import { searchSchema } from './schema';
import RegelRuleModel from './components/RegelRuleModel';
import DetailRegelTime from './components/DetailRegelTime';
import DelayModal from '../components/DelayModal';
import ExecutionModal from '../components/ExecutionModal';

import '../index.less';

import api from '@/api';

const RealTimeRule = () => {
  const [state, setState] = useSet({
    loading: false,
    pageSize: 10,
    pageNo: 1,
    total: 0,
    formData: {}, //搜索
    dataSource: [],
    dataTypeList: {}, //货品类型
    showCreateModal: false, //创建标签弹框存在与否
    detailModal: false, //详情弹框
    detailFormData: null, //详情内容
    showDelayModal: false, //延期
    showExecutionModal: false, //执行记录
    uuid: '', //odps表名/ 接口版本
    ids:'', //延期id‘
    type: 'add', //编辑或新建
    tagNameCn: ''
  });

  const {
    loading,
    pageSize,
    pageNo,
    total,
    showCreateModal,
    detailModal,
    detailFormData,
    dataSource,
    formData,
    dataTypeList,
    showDelayModal,
    showExecutionModal,
    uuid,
    ids,
    type,
    tagNameCn
  } = state;

  const form = useForm();

  useEffect(() => {
    api.rule.dataSource().then((res: any) => {
      if (res.code === '200') {
        setState({
          dataTypeList: res.data,
        });
      } else {
        message.error(res.msg);
      }
    });
    getData();
  }, []);

  // 第一次请求
  const getData = () => {
    const data = {
      pageIndex: pageNo,
      pageSize,
    };
    setState({
      loading: true,
    });
    api.rule.getOdpsRuleConfigList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data.data,
          pageNo: res.data.pageNo,
          pageSize: res.data.pageSize,
          total: res.data.total,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  //重新加载
  const reloadList = () => {
    if (formData && formData.submitOwner && formData.submitOwner.length > 0) {
      formData.submitOwner = formData.submitOwner.toString();
    }
    const data = {
      pageIndex: pageNo,
      pageSize,
      ...formData,
    };
    setState({
      loading: true,
    });
    api.rule.getOdpsRuleConfigList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data.data,
          pageNo: res.data.pageNo,
          pageSize: res.data.pageSize,
          total: res.data.total,
          formData: formData,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  // 查看详情
  const openDetail = (record: any) => {
    setState({ detailModal: true, detailFormData: record });
  };

  // 编辑
  const openEdit = (record: any) => {
    setState({ showCreateModal: true, detailFormData: record, type: 'edit' });
  };

  // 切换页码
  const onPaginationChange = (pageNum: number, pageSize: any) => {
    const data = {
      pageIndex: pageNum,
      pageSize,
      ...formData,
    };
    setState({
      loading: true,
    });
    api.rule.getOdpsRuleConfigList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data.data,
          pageNo: res.data.pageNo,
          pageSize: res.data.pageSize,
          total: res.data.total,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  const onFinish = (formData: any, validation: any, remoteResult: any) => {
    if (formData && formData.submitOwner && formData.submitOwner.length > 0) {
      formData.submitOwner = formData.submitOwner.toString();
    }
    const data = {
      pageIndex: 1,
      pageSize,
      ...formData,
    };
    setState({
      loading: true,
    });
    api.rule.getOdpsRuleConfigList(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data.data,
          pageNo: res.data.pageNo,
          pageSize: res.data.pageSize,
          total: res.data.total,
          formData: formData,
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  // 关闭标签
  const close = () => {
    setState({
      showCreateModal: false,
      detailModal: false,
      detailFormData: null,
      showDelayModal: false,
      showExecutionModal: false,
      uuid: '',
      ids:'',
      tagNameCn:''
    });
  };

  // 启用停用状态
  const onRuleStatus = (record: any) => {
    const data = {
      id: record.id,
      status: record.status === 0 ? 1 : 0,
    };
    api.rule.updateOdpsRuleStatus(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        reloadList();
      } else {
        message.error(res.msg);
      }
    });
  };

  //延期
  const openDelay = (record: any) => {
    setState({ showDelayModal: true, ids:record?.id  });
  };

  //执行记录
  const openExecution = (record: any) => {
    setState({ showExecutionModal: true, uuid: record?.tableName, tagNameCn: record?.tagNameCn });
  };

  const columns: ColumnProps<createTagProps>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '规则名称',
      dataIndex: 'tagNameCn',
      key: 'tagNameCn',
      ellipsis: true,
      render: (text: any, record: any, index: number) => {
        return (
          <Popover content={text}>
            {
              <div
                className="ellipse"
                onClick={() => openExecution(record)}
              >
                {text}
              </div>
            }
          </Popover>
        );
      },
    },
    {
      title: '所属业务',
      dataIndex: 'business',
      key: 'business',
    },
    {
      title: '货品类型',
      dataIndex: 'dataType',
      key: 'dataType',
      render: (text: any, record: any, index: number) => {
        let dataType;
        if (dataTypeList && Object.getOwnPropertyNames(dataTypeList).length > 0) {
          Object.keys(dataTypeList).map((item: any) => {
            if (text.toUpperCase() === dataTypeList[item]) {
              dataType = item;
            }
          });
        }
        return dataType;
      },
    },
    {
      title: '过期时间',
      dataIndex: 'expiredTime',
      key: 'expiredTime',
      render: (text: any, record: any, index: number) => {
        if (text) {
          return text;
        } else {
          return '暂无';
        }
      },
    },
    {
      title: 'Odps表',
      dataIndex: 'tableName',
      key: 'tableName',
      ellipsis: true,
      render: (text: any, record: any, index: number) => {
        return <Popover content={text}>{text}</Popover>;
      },
    },
    {
      title: '更新机制',
      dataIndex: 'updateCycle',
      key: 'updateCycle',
      render: (text: any, record: any, index: number) => {
        if (text === '0 0 12 * * ?') {
          return '每天更新';
        } else if (text === '0 1 9-23 * * ?') {
          return '每小时更新';
        } else if (text === 'never') {
          return '永不更新';
        }
      },
    },
    {
      title: '规则说明',
      dataIndex: 'tips',
      key: 'tips',
      ellipsis: true,
      render: (text: any, record: any, index: number) => {
        return <Popover content={text}>{text}</Popover>;
      },
    },
    {
      title: '对接人员',
      dataIndex: 'submitOwner',
      key: 'submitOwner',
    },
    {
      title: '操作',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      fixed: 'right',
      width: 300,
      render: (text: any, record: any, index: number) => {
        return (
          <div>
            {/* <Button type="link" onClick={() => openDetail(record)}>
              详情
            </Button> */}
            <Button type="link" onClick={() => openEdit(record)}>
              编辑
            </Button>
            {record.status === 1 ? (
              <Button type="link" onClick={() => onRuleStatus(record)}>
                暂停
              </Button>
            ) : null}
            {record.status === 0 ? (
              <Button type="link" onClick={() => onRuleStatus(record)}>
                启用
              </Button>
            ) : null}
            <Button type="link" onClick={() => openDelay(record)}>
              延期
            </Button>
            <Button type="link" onClick={() => openExecution(record)}>
              执行记录
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Card className="create-tag-main">
        <div className="create-tag-top">
          <FormRender
            form={form}
            schema={searchSchema}
            onFinish={onFinish}
            widgets={{
              searchUser: SearchUser,
            }}
          />
          <div className="create-tag-right">
            <Button
              className="create-tag-btnleft"
              onClick={() => {
                form.submit();
              }}
            >
              查询
            </Button>
            <Button
              className="create-tag-btnleft"
              onClick={() => {
                form.resetFields();
                getData();
                setState({
                  formData: {},
                });
              }}
            >
              重置
            </Button>
            <Button
              className="create-tag-btnleft"
              type="primary"
              onClick={() => {
                setState({ showCreateModal: true, type:'add' });
              }}
            >
              创建规则
            </Button>
          </div>
        </div>
        <Table
          scroll={{ x: 1300 }}
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey={(record) => record.id}
          pagination={{
            pageSize: pageSize,
            current: pageNo,
            total: total,
            onChange: (num, pageSize) => onPaginationChange(num, pageSize),
          }}
        />
      </Card>
      <DetailRegelTime
        detailFormData={detailFormData}
        detailModal={detailModal}
        close={close}
        dataTypeList={dataTypeList}
      />
      {showCreateModal ? <RegelRuleModel showCreateModal={showCreateModal} reloadList={reloadList} close={close} type={type} detailFormData={detailFormData}/> : null}
      {showDelayModal ? <DelayModal showDelayModal={showDelayModal} close={close} reloadList={reloadList} ids={ids}/> : null}
      {showExecutionModal ? <ExecutionModal showExecutionModal={showExecutionModal} close={close} uuid={uuid} tagNameCn={tagNameCn} modalType='regel'/> : null}
    </>
  );
};

export default RealTimeRule;
