import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { Modal, message, Form, Select, Cascader, Input, Row, Col } from 'antd';

import SearchUser from '@/components/UserSearch';
import RegelOdps from '../RegelOdps';
import ExpirationTime from '@/components/ExpirationTime';
import { get } from 'lodash';
import { getVal } from '@/utils/utils';

import api from '@/api';
const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const { Option } = Select;
const { TextArea } = Input;

const RegelRuleModel = (props: any) => {
  const { showCreateModal, close, reloadList, detailFormData, type } = props;

  const [state, setState] = useSet({
    dataTypeList: {},
    optionsData: [],
  });

  const { dataTypeList, optionsData } = state;

  const [form] = Form.useForm();

  useEffect(() => {
    api.rule.dataSource().then((res: any) => {
      if (res.code === '200') {
        setState({
          dataTypeList: res.data,
        });
      } else {
        message.error(res.msg);
      }
    });

    if (type === 'edit') {
      api.rule.GetOdpsRuleConfig({ id: detailFormData?.id }).then((res: any) => {
        if (res.code === '200' && res.data) {
          // onBackFill(get(res.data, 'dataType',''),res.data)
          form.setFieldsValue({
            // business: get(res.data,'business',''),
            // tagNameCn: get(res.data,'tagNameCn',''),
            // dataType: get(res.data, 'dataType','')?.toUpperCase(),
            // ruleDom: get(res.data,'ruleDom',''),
            // tips: get(res.data, 'tips',''),
            // ruleOp: get(res.data, 'ruleOp',''),
            // ruleCategory: getVal(optionsData,get(res.data, 'ruleCategory',''),[]),
            tableName: get(res.data, 'tableName', ''),
            templateCode: get(res.data, 'templateCode', undefined),
            d2AccessId: get(res.data, 'd2AccessId', ''),
            d2AccessKey: get(res.data, 'd2AccessKey', ''),
            updateCycle: get(res.data, 'updateCycle', ''),
            submitOwner: get(res.data, 'submitOwner', []),
            fieldStr: get(res.data, 'fieldStr', ''),
            fieldUnique: get(res.data, 'fieldUnique', ''),
            menuVersion: get(res.data, 'menuVersion', ''),
            ds: get(res.data, 'ds', undefined),
            expiredTime: get(res.data, 'expiredTime', undefined),
            condition: get(res.data, 'condition', undefined),
          });
        }
      });
    }
  }, []);

  const createFinish = (formData: any) => {
    formData.submitOwner = formData.submitOwner.toString();
    // formData.ruleCategory = formData.ruleCategory[formData.ruleCategory.length - 1];
    const data = {
      ...formData,
    };

    if (type === 'edit') {
      api.rule.UpdateOdpsRuleConfig({ id: detailFormData?.id, ...data }).then((res: any) => {
        if (res.code === '200') {
          message.success('修改成功');
          reloadList();
          close();
        } else {
          message.error(res.msg);
        }
      });
    } else {
      api.rule.createOdpsRuleConfig(data).then((res: any) => {
        if (res.code === '200') {
          message.success(res.msg);
          close();
          reloadList();
        } else {
          message.error(res.msg);
        }
      });
    }
  };

  const createOk = () => {
    form.submit();
  };

  const createCancel = () => {
    close();
    form.resetFields();
  };

  const onDataType = (value: any) => {
    if (value) {
      const data = {
        dataType: value,
      };
      api.rule.getRuleLevelTree(data).then((res: any) => {
        if (res.code === '200') {
          setState({ optionsData: res.data.childNode });
          // form.setFieldsValue({
          //   ruleCategory: undefined,
          // })
        }
      });
    }
  };

  // 回填展示位置
  const onBackFill = (value: any, formData: any) => {
    if (value) {
      const data = {
        dataType: value,
      };
      api.rule.getRuleLevelTree(data).then((res: any) => {
        if (res.code === '200') {
          setState({ optionsData: res.data.childNode });
          form.setFieldsValue({
            ruleCategory: getVal(res.data.childNode, Number(get(formData, 'ruleCategory', '')), []),
          });
        }
      });
    }
  };

  return (
    <Modal
      title={type === 'edit' ? '编辑规则' : '新增规则'}
      visible={showCreateModal}
      width={'80vw'}
      onOk={createOk}
      onCancel={createCancel}
    >
      <Form form={form} onFinish={createFinish} {...layout}>
        <Row gutter={24}>
          {type === 'add' ? (
            <>
              <Col span={12}>
                <Form.Item label="规则名称" name="tagNameCn" rules={[{ required: true, message: '请输入规则名称' }]}>
                  <Input maxLength={20} placeholder="选品筛选条件名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="所属业务" name="business" rules={[{ required: true, message: '请输入所属业务' }]}>
                  <Input maxLength={20} placeholder="请输入所属业务，例如：度假行业-门票/套餐" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="dataType" label="货品类型" rules={[{ required: true, message: '请选择货品类型' }]}>
                  <Select onChange={onDataType} placeholder="请选择货品类型">
                    {dataTypeList &&
                      Object.getOwnPropertyNames(dataTypeList).length > 0 &&
                      Object.keys(dataTypeList).map((item: any) => {
                        return (
                          <Option key={dataTypeList[item]} value={dataTypeList[item]} label={item}>
                            {item}
                          </Option>
                        );
                      })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="ruleDom" label="规则样式" rules={[{ required: true, message: '请选择规则样式' }]}>
                  <Select placeholder="请选择规则样式">
                    <Option value="CHECKBOX">下拉框</Option>
                    <Option value="INPUT">输入框</Option>
                    <Option value="RADIO">单选框</Option>
                    <Option value="TREE">树状图</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="规则说明"
                  name="tips"
                  rules={[{ required: true, message: '请输入规则说明' }]}
                  labelCol={{ span: 3 }}
                  wrapperCol={{ span: 21 }}
                >
                  <TextArea showCount maxLength={50} placeholder="请输入规则说明" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="ruleOp" label="规则逻辑" rules={[{ required: true, message: '请选择规则逻辑' }]}>
                  <Select placeholder="请选择规则逻辑">
                    <Option value="BETWEEN_AND">区间</Option>
                    <Option value="EQUAL">等于</Option>
                    <Option value="GT">大于</Option>
                    <Option value="GTE">大于等于</Option>
                    <Option value="LT">小于</Option>
                    <Option value="LTE">小于等于</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="ruleCategory" label="展示位置" rules={[{ required: true }]}>
                  <Cascader
                    style={{ width: '100%' }}
                    options={optionsData}
                    placeholder="请选择展示位置"
                    fieldNames={{ label: 'value', value: 'id', children: 'childNode' }}
                  ></Cascader>
                </Form.Item>
              </Col>
            </>
          ) : null}
          <Col span={24}>
            <Form.Item
              name="tableName"
              label="Odps表名"
              rules={[{ required: true }]}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 21 }}
            >
              <RegelOdps></RegelOdps>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="D2账号" name="d2AccessId" rules={[{ required: true, message: '请输入D2账号' }]}>
              <Input placeholder="请生产表同学填写或提供该配置项内容" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="D2密码" name="d2AccessKey" rules={[{ required: true, message: '请输入D2账号' }]}>
              <Input placeholder="请生产表同学填写或提供该配置项内容" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="templateCode" label="字段模版" rules={[{ required: true, message: '请选择字段模版' }]}>
              <Select placeholder="请选择字段模版">
                <Option value={1}>模版一</Option>
                <Option value={2}>模版二</Option>
                <Option value={3}>模版三</Option>
                <Option value={4}>模版四</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="updateCycle" label="更新机制" rules={[{ required: true, message: '请选择更新机制' }]}>
              <Select placeholder="请选择更新机制">
                <Option value="0 0 12 * * ?">每天更新</Option>
                <Option value="0 1 9-23 * * ?">每小时更新</Option>
                <Option value="never">永不更新</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="submitOwner" label="对接人员" rules={[{ required: true, message: '请选择对接人员' }]}>
              <SearchUser mode="multiple"></SearchUser>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="读取字段" name="fieldStr">
              <Input placeholder="请输入读取字段" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="表唯一字段" name="fieldUnique">
              <Input placeholder="请输入表唯一字段" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="菜单接口版本" name="menuVersion">
              <Input placeholder="请输入菜单接口版本" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="分区" name="ds">
              <Input placeholder="请输入分区（指定分区（需要传 20210101）" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="SQL条件" name="condition">
              <Input placeholder="请输入SQL条件" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="过期时间" name="expiredTime">
              <ExpirationTime></ExpirationTime>
            </Form.Item>
          </Col>
        </Row>
        <p style={{ paddingLeft: '6%' }}>注：有疑问请联系长弘～</p>
      </Form>
    </Modal>
  );
};

export default RegelRuleModel;
