import React, { useState, useEffect } from 'react';
import { Input, Popover, Button } from 'antd';
import _ from 'lodash';
import { QuestionCircleOutlined } from '@ant-design/icons';
const RegelOdps = (props: any) => {
  let { value = '', onChange } = props;

  const [data, setData] = useState<any[]>([]);

  const content = (
    <Button type="link" onClick={()=> {window.open("https://yuque.antfin-inc.com/docs/share/d2711668-4512-41ba-ad0f-7ef5cb7715a2?#");}}>跳转到接入说明</Button>
  );

  const handleChange = (value: any) => {
    onChange(value)
  };

  return (
    <div style={{display:'flex',alignItems:"center",width:"100%"}}>
      <Input placeholder="请输入表名如:trip_vacation.table_name" style={{width:'75%',marginRight:'10px'}} onChange = {handleChange} value={value}/>
      <Popover content={content}>
        <div style={{cursor:'pointer'}}>
          <QuestionCircleOutlined />
          接入说明
        </div>
      </Popover>
    </div>
  );
};

export default RegelOdps;
