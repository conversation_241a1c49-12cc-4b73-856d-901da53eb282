import React from 'react';
import { Button, Card } from 'antd';
import { history } from 'umi';

import createRuleOne from '@/components/Images/createRuleOne.png';
import createRuleTwo from '@/components/Images/createRuleTwo.png';

import './index.less';

const createRuleList = [
  {
    imgSrc: createRuleOne,
    title: '实时规则',
    describe: '通过接口对接生成选品规则，需对接方开发同学介入共同完成对接',
    btnName: '立即对接',
    pathUrl: '/createRule/realTimeRule',
  },
  {
    imgSrc: createRuleTwo,
    title: '离线规则',
    describe: '通过Odps表对接生成选品规则，业务同学通过填写Odps表相关信息即可完成规则创建',
    btnName: '一键接入',
    pathUrl: '/createRule/regelRule',
  },
];

const CreateRule = () => {
  return (
    <Card className="create-rule-wrap">
      <div className="create-rule-main">
        {createRuleList.map((ele: any, i: number) => {
          return (
            <div className="create-rule-list" key={i}>
              <div className="create-rule-img">
                <img src={ele.imgSrc} />
              </div>
              <div className="create-rule-title">{ele.title}</div>
              <div className="create-rule-describe">{ele.describe}</div>
              <Button
                type="primary"
                shape="round"
                onClick={() => {
                  history.push(ele.pathUrl);
                }}
              >
                {ele.btnName}
              </Button>
            </div>
          );
        })}
      </div>
    </Card>
  );
};

export default CreateRule;
