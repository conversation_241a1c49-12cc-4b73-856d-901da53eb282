import React, { useState, useEffect } from 'react';
import { message, Form, Card, Button, Input, DatePicker, InputNumber } from 'antd';
import { useSet } from '@/utils/hooks';
import BraftEditor from 'braft-editor';
import moment from 'moment';

import 'braft-editor/dist/index.css';
import api from '@/api';

import styles from './index.less';

const { RangePicker } = DatePicker;

const FilterQuery = () => {
  // const [form] = Form.useForm(); // 启动屏告示相关，已隐藏
  const [formTwo] = Form.useForm();

  useEffect(() => {
    api.activity.notice().then((res: any) => {
      if (res.code === '200' && res.data && res.data.content && res.data.start && res.data.end) {
        formTwo.setFieldsValue({
          content: BraftEditor.createEditorState(decodeURIComponent(res.data.content)),
          picker: [moment(res.data.start), moment(res.data.end)],
        });
      }
    });

    // 启动屏告示相关接口调用已隐藏
    // api.activity.homePageNotice().then((res: any) => {
    //   if (res.code === '200' && res.data && res.data.content && res.data.start && res.data.end) {
    //     form.setFieldsValue({
    //       content: BraftEditor.createEditorState(decodeURIComponent(res.data.content)),
    //       picker: [moment(res.data.start), moment(res.data.end)],
    //       time: res.data.time
    //     });
    //   }
    // });
  }, []);

  // 启动屏告示处理函数已隐藏
  // const onHomeNotice = (values: any) => {
  //   const data: any = {
  //     content: encodeURIComponent(values?.content?.toHTML()),
  //     start: moment(values['picker'][0]).valueOf(),
  //     end: moment(values['picker'][1]).valueOf(),
  //     time: values.time
  //   };

  //   api.activity.saveHomePageNotice(data).then((res: any) => {
  //     if (res.code === '200') {
  //       message.success(res.msg);
  //     } else {
  //       message.error(res.msg);
  //     }
  //   });
  // };

  // 小喇叭提交
  const onTrumpet = (values: any) => {
    const data: any = {
      content: encodeURIComponent(values?.content?.toHTML()),
      start: moment(values['picker'][0]).valueOf(),
      end: moment(values['picker'][1]).valueOf(),
    };

    api.activity.saveNotice(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
      } else {
        message.error(res.msg);
      }
    });
  };

  return (
    <>
      {/* 启动屏告示部分已隐藏 */}
      {/* <Card style={{ marginBottom: '20px' }} bordered={false}>
        <p>启动屏告示</p>
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 10 }} onFinish={onHomeNotice} form={form}>
          <Form.Item
            label="告示内容"
            name="content"
            validateTrigger="onBlur"
            rules={[
              { required: true, message: '' },
              {
                validator: (_, vaule) => {
                  if (!vaule) {
                    return Promise.reject('请输入告示内容');
                  } else {
                    if (vaule.toHTML() === '<p></p>') {
                      return Promise.reject('请输入告示内容');
                    } else {
                      return Promise.resolve();
                    }
                  }
                },
              },
            ]}
          >
            <BraftEditor contentStyle={{ height: 305 }} className={styles.editor_border} />
          </Form.Item>
          <Form.Item name="picker" label="展示时间" rules={[{ required: true, message: '请输入展示时间' }]}>
            <RangePicker style={{ width: '100%' }} showTime />
          </Form.Item>
          <Form.Item
            name="time"
            label="屏显时间"
            rules={[{ required: true, pattern: new RegExp(/^[1-9]\d*$/), message: '请输入正确的展示时间' }]}
          >
            <Input addonAfter="秒" />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Button type="primary" htmlType="submit" shape="round">
              发布
            </Button>
          </Form.Item>
        </Form>
      </Card> */}
      <Card bordered={false}>
        <p>小喇叭消息</p>
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 10 }} onFinish={onTrumpet} form={formTwo}>
          <Form.Item
            label="告示内容"
            name="content"
            validateTrigger="onBlur"
            rules={[
              { required: true, message: '' },
              {
                validator: (_, vaule) => {
                  if (!vaule) {
                    return Promise.reject('请输入告示内容');
                  } else {
                    if (vaule.toHTML() === '<p></p>') {
                      return Promise.reject('请输入告示内容');
                    } else {
                      return Promise.resolve();
                    }
                  }
                },
              },
            ]}
          >
            <BraftEditor contentStyle={{ height: 305 }} className={styles.editor_border} />
          </Form.Item>
          <Form.Item name="picker" label="展示时间" rules={[{ required: true, message: '请输入展示时间' }]}>
            <RangePicker style={{ width: '100%' }} showTime />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Button type="primary" htmlType="submit" shape="round">
              发布
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </>
  );
};

export default FilterQuery;
