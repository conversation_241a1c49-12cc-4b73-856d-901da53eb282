import activity from './activity';
import tag from './tag';
import rule from './rule';
import ruleType from './ruleType';
import { formDataApi } from './common';

const common = {
  /** 获取登陆用户信息 */
  getUserInfo: () => formDataApi('/common/currentUserInfo.do'),
  /** 获取登陆用户权限信息 */
  // /common/AuthInfo
  getAuthInfo: () => formDataApi('/common/AuthInfo'),
  // GetCommonSuggest
  getCommonSuggest: ({ query, key }: { query: string; key: string }) =>
    formDataApi(`/common/GetCommonSuggest?query=${query}&key=${key}`),
  /** 判断是否为星辰管理员 */
  isGalaxyAdmin: () => formDataApi('/common/IsGalaxyAdmin.do'),
};

const api = {
  common,
  activity,
  tag,
  rule,
  ruleType,
  odps: {
    createActivityByOdps: (params: any) => formDataApi('/odps/CreateActivityByOdps.do', params, 'POST'),
    queryOdpsTable: (params: any) => formDataApi('/odps/QueryDwsTableInfo.do', params, 'post'),
    checkOdpsAuth: (params: any) => formDataApi('/odps/CheckOdpsAuth.do', params, 'post'),
    queryDwsTableColumn: (params: any) => formDataApi('/odps/QueryDwsTableColumn.do', params, 'post'),
    queryDwsTablePartition: (params: any) => formDataApi('/odps/QueryDwsTablePartition.do', params, 'post'),
    getOdpsActivityResult: (params: any) => formDataApi('/odps/GetOdpsActivityResult.do', params, 'post'),
  },
};

export default api;
