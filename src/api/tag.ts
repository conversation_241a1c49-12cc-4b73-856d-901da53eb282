import { runApi, formDataReq } from './common';

// 这里集中写一些处理入参和出参的胶水函数：
const handleParams = (params: any) => params;
const handleResult = (result: any) => {
  return result.data;
};

const tag = {
  tagList: (params: {
    pageNo: number | string;
    pageSize: number | string;
    tagId?: string;
    tagName?: string;
    creator?: string;
    owner?: string;
  }) => runApi('/tag/tagList.do', handleParams(params)).then(handleResult),
  applyTag: (params: {
    belongTo: number | string;
    tagId: number | string;
    tagName: string;
    description: string;
    owner: any;
  }) => runApi('/tag/ApplyTag.do', handleParams(params)),
  tagActivityList: (params: { id: number }) => runApi('/tag/TagActivityList.do', handleParams(params)),
  saveTag: (params: { id: number; description: string; owner: any }) => runApi('/tag/SaveTag.do', handleParams(params)),
  acceptTag: (params: { id: number; status: number; belongTo: number }) =>
    runApi('/tag/AcceptTag.do', handleParams(params)), // 通过审核
  refuseTag: (params: { id: number; status: number; belongTo: number }) =>
  runApi('/tag/RefuseTag.do', handleParams(params)), // 拒绝审核
};

export default tag;
