import { runApi, formDataReq, handleParams, handleResult } from './common';


const ruleType = {
  getTreeData: ({ id = 'TRAVEL' }) => {
    if (id === 'categoryId') {
      return runApi('/activity/CategoryList.do?category=TRAVEL');
    }
    if (id === 'playId') {
      return runApi('/activity/PlayList.do');
    }
    return runApi(`/activity/GetTreeDataByType.do?type=${id}`);
  },
  doubleSelect: (params:any) => runApi(`/activity/GetTreeDataByType.do?${params}`),
}

export default ruleType;