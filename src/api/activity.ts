import { extend } from 'umi-request';
import { runApi, formDataApi, handleParams, handleResult } from './common';

export interface ActivityListParam {
  parentId: number;
  pageNo: number;
  pageSize: number;
  isOwner: boolean;
  isChild: number;
  query: string;
  id?: string;
  title?: string;
  dataSource?: string;
  status?: string;
}

export const fileUpload = extend({
  // prefix: '/api', // 如果有统一的前缀
  credentials: 'include', // 默认请求是否带上cookie
  headers: {
    'Content-Type': 'multipart/form-data',
  },
});

const activity = {
  list: (params: ActivityListParam) =>
    runApi('/activity/ActivityList.do', handleParams(params)).then(handleResult('/activity/ActivityList.do')),
  /**  保存主活动/保存子活动都是这个接口 */
  // top搜索接口
  topSearchList:(params: any) =>
  runApi('/activity/TopSearchList.do', handleParams(params)).then(handleResult('/activity/TopSearchList.do')),
  start: (params: any) => runApi('/activity/StartActivity.do', handleParams(params)),
  delete: (params: any) => runApi('/activity/deleteActivity.do', handleParams(params)),
  stop: (params: any) => runApi('/activity/StopActivity.do', handleParams(params)),
  /** 货品类型 */
  dataSourceList: () => runApi('/activity/dataSourceList.do').then(handleResult('/activity/dataSourceList.do')),
  save: (params: any) => formDataApi('/activity/saveActivity.do', params, 'post'),
  batchDelayActivity: (params: any) => formDataApi('/activity/BatchDelayActivity.do', params, 'post'),
  copy: (params: any) => formDataApi('/activity/copyActivity.do', params, 'post'),
  getActivity: (params: { id: string | number; fetch: boolean }) =>
    formDataApi('/activity/GetActivity.do', params, 'post'),
  /** 选品预览 */
  preview: (params: {
    id: number;
    ruleMap?: string;
    ruleGroup?: string;
    pageNo: number;
    pageSize: number;
    extQuery?: {};
  }) => {
    return formDataApi('/activity/preview.do?_input_charset=UTF-8&_output_charset=UTF-8', params, 'POST');
  },
  /** 保存活动规则 */
  saveActivityRule: (params: { id: number; ruleGroup: string; limitCount?: number }) => {
    return formDataApi(
      '/activity/SaveActivityRule.do',
      {
        _input_charset: 'UTF-8',
        _output_charset: 'UTF-8',
        ...params,
      },
      'POST',
    );
  },
  /** 获取当前用户的可用标签列表 */
  tagList: (params: { activityId: number; owner: string }) =>
    formDataApi(
      '/tag/tagList.do',
      {
        ...params,
        filterNotAccept: 'true',
      },
      'GET',
    ),
  /** 发布活动 */
  publish: (params: { id: number; itemCount: number; tags?: string }) => {
    return formDataApi('activity/Publish.do', params, 'GET');
  },
  /** 去除商品 */
  saveBlack: (params: { activityId: number; itemIds: number[] }) => {
    return formDataApi(
      '/activity/SaveBlack.do',
      {
        _input_charset: 'UTF-8',
        _output_charset: 'UTF-8',
        ...params,
      },
      'POST',
    );
  },
  /** excel上传完成后，将获取到的excel地址存入这个接口 */
  createActivityByExcel: (params: { excelUrl: string; activityId: number,operation: any }) => {
    return formDataApi('/activity/CreateActivityByExcel.do', params, 'POST');
  },

  /** 获取打标列表*/
  GetActivityTagList:(params: any) => runApi('/activity/GetActivityTagList.do', handleParams(params)),

  /** 删除打标任务*/
  RemoveActivityTag:(params: any) => runApi('/activity/RemoveActivityTag.do', handleParams(params)),

  /** 过滤查询*/
  QueryFilterReason: (params: {activityId: string,itemIds: string,type: string}) => runApi('/activity/QueryFilterReason', handleParams(params)),
  
  // Top公告查询
  notice: () => runApi('/activity/Notice.do'),

  // TOP公告保存
  saveNotice: (params: { content: string; start:string ,end: string }) => {
    return formDataApi('/activity/saveNotice.do', params, 'POST');
  },

  // 首页公告查询
  homePageNotice: () => runApi('/home/<USER>'),

  // 首页公告保存
  saveHomePageNotice: (params: { content: string; start:string ,end: string, time: string }) => {
    return formDataApi('/home/<USER>', params, 'POST');
  },

  // 已剔除商品列表
  blackList: (params: {activityId: string}) => runApi('/activity/black/BlackList.do', handleParams(params)),

  // 移除黑名单
  removeBlack: (params: { activityId: string; itemIds:string }) => {
    return formDataApi('/activity/black/RemoveBlack.do', params, 'POST');
  },
  //过滤商品查询
  uploadItemFilterCheck: (params: {activityId: string}) => runApi('/activity/UploadItemFilterCheck.do', handleParams(params)),

  //导出excel至玄机藏
  exportItem: (params: {activityId: string,itemCount?: number}) => runApi('/activity/export/exportItem.do', handleParams(params)),

  //查询操作历史
  GetActivityOpRecord:(params: {id: number| string, pageNo: number, pageSize: number, operation: any}) => runApi('/activity/GetActivityOpRecord', handleParams(params)).then(handleResult('/activity/GetActivityOpRecord')),

  //一键重跑
  OneClickPublish: (params: {id: string| number}) => runApi('/activity/OneClickPublish.do', handleParams(params)),

  //执行记录
  GetTaskRecord:(params: {uuid: number| string, pageNo: number, pageSize: number}) => runApi('/task/GetTaskRecord', handleParams(params)).then(handleResult('/task/GetTaskRecord')),
  

  //延期规则
  ExtendExpireTime: (params: { id: number; days:number }) => {
    return formDataApi('/odps/ExtendExpireTime', params, 'POST');
  },

  //上传类型
  ExcelImportToolController: (params:any) => formDataApi('/tools/ExcelImportToolController', params, 'POST'),

  // excel上传至odps历史记录
  ExcelImportToolOpRecord:(params: any) => runApi('/tools/ExcelImportToolOpRecord', handleParams(params)),

  // 下载模板
  downloadTemp: (params:any) => formDataApi('/activity/DownloadPic', params, 'POST').then(handleResult('/activity/DownloadPic')),

  // 新品手动刷新工具
  manualRefreshItemStatus: (params: { dataSource: string; itemIds: string }) => 
    formDataApi('/tools/ManualRefreshItemStatus.do', params, 'POST'),
};

export default activity;
