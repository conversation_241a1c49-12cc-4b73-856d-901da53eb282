import { runApi, formDataReq, handleParams, handleResult } from './common';

const objForData = (params: any) => {
  let ret = '';
  for (let it in params) {
    ret += encodeURIComponent(it) + '=' + encodeURIComponent(params[it]) + '&';
  }
  ret = ret.substring(0, ret.lastIndexOf('&'));
  return ret;
};

const formDataApi = async (api: string, params?: any, method = 'get') => {
  const options: any = { method };
  if (method.toUpperCase() === 'GET') {
    options.params = params;
  }
  if (method.toUpperCase() === 'POST') {
    options.data = objForData(params);
  }
  return formDataReq(api, options)
    .then((res) => {
      if (res.code === '302') {
        if(window.location.href.includes('isFrame=true')){
          location.href = `${res.data.url}?isFrame=true`;
        } else {
          location.href = res.data.url;
        }
        return
      }
      return res;
    })
    .catch((err) => {
      // TODO: 发布了记得去掉
      console.group(api);
      console.log('%cParams:', 'color: #FF4D4F; font-weight: 700;', params);
      console.log('%cResponse:', 'color: #FF4D4F; font-weight: 700;', err);
      console.groupEnd();
    });
};

export interface RuleListParam {
  category: string;
}

const rule = {
  list: (params: RuleListParam) =>
    runApi('/rule/RuleList.do', handleParams(params)).then(handleResult('/rule/RuleList.do')),
  dataSource: () => runApi('/activity/dataSourceList.do'),
  createOdpsRuleConfig: (params: any) => formDataApi('/odps/createOdpsRuleConfig.do', params, 'post'),
  getOdpsRuleConfigList: (params: {
    tableName?: string;
    pageIndex?: string | number;
    pageSize?: string | number;
    submitOwner?: any;
  }) => runApi('/odps/getOdpsRuleConfigList.do', handleParams(params)),
  updateOdpsRuleStatus: (params: any) => formDataApi('/odps/UpdateOdpsRuleStatus.do', params, 'post'),
  getRuleLevelTree: (params: any) => runApi('/rule/GetRuleLevelTree.do', handleParams(params)),
  createRealtimeRuleConfig: (params: any) => formDataApi('/realtime/CreateRealtimeRuleConfig.do', params, 'post'),
  UpdateOdpsRuleConfig: (params: any) => runApi('/odps/UpdateOdpsRuleConfig', handleParams(params)),
  getRealtimeRuleConfigList: (params: {
    tableName?: string;
    pageIndex?: string | number;
    pageSize?: string | number;
    submitOwner?: any;
  }) => runApi('/realtime/GetRealtimeRuleConfigList.do', handleParams(params)),
  updateRealtimeRuleStatus: (params: any) => formDataApi('/realtime/UpdateRealtimeRuleStatus.do', params, 'post'),
  GetOdpsRuleConfig: (params: {id: string| number}) =>
    runApi('/odps/GetOdpsRuleConfig', handleParams(params))
};

export default rule;
