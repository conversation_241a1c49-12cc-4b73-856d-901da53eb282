import { extend } from 'umi-request';
import { notification } from 'antd';

const req = extend({
  // prefix: '/api', // 如果有统一的前缀
  credentials: 'include', // 默认请求是否带上cookie
  // headers: {
  //   'Content-Type': 'multipart/form-data',
  // },
});

export const runApi = async (api: string, params?: any, method = 'get') => {
  const options: any = { method };
  if (method.toUpperCase() === 'GET') {
    options.params = params;
  }
  if (method.toUpperCase() === 'POST') {
    options.data = params;
  }
  return req(api, options)
    .then((res) => {
      if (res.code === '302') {
        if (window.location.href.includes('isFrame=true')) {
          location.href = `${res.data.url}?isFrame=true`;
        } else {
          location.href = res.data.url;
        }
        return;
      }
      return res;
    })
    .catch((err) => {
      // TODO: 发布了记得去掉
      console.group(api);
      console.log('%cParams:', 'color: #FF4D4F; font-weight: 700;', params);
      console.log('%cResponse:', 'color: #FF4D4F; font-weight: 700;', err);
      console.groupEnd();
    });
};

/** form-data 请求 */
export const formDataReq = extend({
  // prefix: '/api', // 如果有统一的前缀
  credentials: 'include', // 默认请求是否带上cookie
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
});

// 这里集中写一些处理入参和出参的胶水函数：
export const handleParams = (params: any) => params;

/**
 * 处理结果
 * @param apiPath 请求路径
 * @returns
 */
export const handleResult = (apiPath: string) => (res: any) => {
  if (res.code === '200' && res.msg === '操作成功') {
    return res.data;
  } else {
    notification.error({
      message: `接口${apiPath}报错`,
      description: res.msg,
    });
  }
};

const objForData = (params: any) => {
  let ret = '';
  for (let it in params) {
    ret += encodeURIComponent(it) + '=' + encodeURIComponent(params[it]) + '&';
  }
  ret = ret.substring(0, ret.lastIndexOf('&'));
  return ret;
};

export const formDataApi = async (api: string, params?: any, method = 'get') => {
  const options: any = { method };
  if (method.toUpperCase() === 'GET') {
    options.params = params;
  }
  if (method.toUpperCase() === 'POST') {
    options.data = objForData(params);
  }
  return formDataReq(api, options)
    .then((res) => {
      if (res.code === '302') {
        if (window.location.href.includes('isFrame=true')) {
          location.href = `${res.data.url}?isFrame=true`;
        } else {
          location.href = res.data.url;
        }
        return;
      }
      return res;
    })
    .catch((err) => {
      // TODO: 发布了记得去掉
      console.group(api);
      console.log('%cParams:', 'color: #FF4D4F; font-weight: 700;', params);
      console.log('%cResponse:', 'color: #FF4D4F; font-weight: 700;', err);
      console.groupEnd();
    });
};
