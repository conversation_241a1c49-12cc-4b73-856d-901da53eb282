export default {
  // GET 可忽略
  '/api/userInfo': {
    data: {
      name: '华安',
      avatar: 'https://gw.alipayobjects.com/zos/r/51lvtb/20200304002418.png',
      userId: '9527',
    },
  },
  '/goods/list': {
    code: '',
    msg: '成功',
    data: {
      rows: [
        {
          id: '1',
          name: '胡彦斌',
          age: 32,
          gender: 'male',
          address: '西湖区湖底公园1号',
        },
        {
          id: '2',
          name: '胡彦祖',
          age: 42,
          gender: 'female',
          address: '西湖区湖底公园1号',
        },
      ],
      total: 200,
    },
    success: true,
  },
  '/goods/mylist': {
    code: '',
    msg: '成功',
    data: {
      rows: [
        {
          id: '1',
          name: '胡彦斌',
          age: 32,
          address: '西湖区湖底公园1号',
        },
      ],
      total: 100,
    },
    success: true,
  },
  // 支持自定义函数，API 参考 express@4
  'POST /goods/add': (req: any, res: any) => {
    // 添加跨域请求头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.end({
      code: '',
      msg: '成功',
      data: {},
      success: true,
    });
  },
  'POST /goods/edit': {
    code: '',
    msg: '成功',
    data: {},
    success: true,
  },
  'POST /goods/del': {
    code: '',
    msg: '成功',
    data: {},
    success: true,
  },
};

