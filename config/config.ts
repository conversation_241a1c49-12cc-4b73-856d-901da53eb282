// https://umijs.org/config/
import { defineConfig } from 'umi';
//@ts-ignore
// import AntdDayjsWebpackPlugin from 'antd-dayjs-webpack-plugin';
import routes from './routes';

export default defineConfig({
  antd: {},
  // mfsu: {},
  fastRefresh: {},
  mountElementId: 'root',
  dva: {
    hmr: true,
  },
  //qiankun: {   // 需要开启微前端需要打开
  //  slave: {},
  //},
  // 开启约束规则检查
  strict: {
    rules: {
      CODE_PREFER_USE_AT: false,
      CODE_COMPONENT_COMPLEX: false,
      CODE_NO_CROSS_PAGE_GROUP_IMPORT: false,
    },
  },
  // 开启simpleAem，项目申请地址: https://aem.alibaba-inc.com/myproject
  // simpleAem:{
  //   pid: 'jkpci'
  // },
  history: { type: 'hash' },
  locale: {
    // default zh-CN
    default: 'zh-CN',
    // default true, when it is true, will use `navigator.language` overwrite default
    antd: true,
    baseNavigator: true,
  },
  esbuild: {},
  devtool: 'eval',
  targets: {
    ie: 11,
  },
  // umi routes: https://umijs.org/docs/routing
  routes,
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    // 如果要设置dark mode
    'primary-color': '#1890ff',
  },
  title: false,
  ignoreMomentLocale: true,

  // 打包相关
  lessLoader: {},
  dynamicImport: false,
  // chainWebpack(config, { webpack }) {
  //   config.plugin('dayjs').use(AntdDayjsWebpackPlugin);
  // },
  extraBabelPlugins: [
    ['import', { libraryName: 'lodash', libraryDirectory: '', camel2DashComponentName: false }, 'lodash'],
  ],
  hash: false,

  // 纯前端的配置
  outputPath: './build',
  // 除去 midway7-faas 外的都用这个
  manifest: {
    basePath: '/',
  },
});
