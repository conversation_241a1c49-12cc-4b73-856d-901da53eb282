// 路由配置方法详见文档：
// umi: https://umijs.org/zh-CN/docs/routing
// antd pro: https://pro.ant.design/docs/router-and-nav-cn

export default [
  {
    path: 'login',
    component: '@/components/Loading',
  },
  {
    path: '/',
    component: '@/layouts/index',
    routes: [
      {
        path: '/',
        component: './Welcome',
      },
      {
        name: '创建品集',
        icon: 'ShoppingOutlined',
        path: '/createCollect',
        hideInMenu: true,
        component: './CreateCollect',
      },
      {
        name: '品集管理',
        icon: 'SettingOutlined',
        path: '/manageCollect',
        component: './ManageCollect',
      },
      {
        name: '线上选品池',
        icon: 'SettingOutlined',
        path: '/createCollect/goodsPool',
        hideInMenu: true,
        component: './GoodsPool',
      },
      {
        name: '品集管理/子品集',
        icon: 'SettingOutlined',
        path: '/manageCollect/manageChild',
        hideInMenu: true,
        component: './ManageCollect/ManageChild',
      },
      {
        name: '品集管理/子品集/编辑',
        icon: 'SettingOutlined',
        path: '/manageChildEdit',
        hideInMenu: true,
        component: './ManageCollect/ManageChild/ManageChildEdit',
      },
      {
        name: 'ICTag关联',
        icon: 'BlockOutlined',
        path: '/createTag',
        component: './CreateTag',
      },
      {
        name: '规则接入',
        icon: 'BulbOutlined',
        path: '/createRule',
        hideInMenu: true,
        component: './CreateRule',
      },
      {
        name: '规则接入/实时规则',
        icon: 'BulbOutlined',
        path: '/createRule/realTimeRule',
        hideInMenu: true,
        component: './CreateRule/RealTimeRule',
      },
      {
        name: '规则接入/离线规则',
        icon: 'BulbOutlined',
        path: '/createRule/regelRule',
        hideInMenu: true,
        component: './CreateRule/RegelRule',
      },
      {
        name: '权限列表',
        icon: 'BulbOutlined',
        path: '/jurisdiction',
        hideInMenu: true,
        component: './Jurisdiction',
      },
      {
        name: '数据大盘',
        icon: 'PieChartOutlined',
        path: '/dataMarket',
        hideInMenu: true,
        component: './DataMarket',
      },      {
        path: '/tool',
        name: '自助工具',
        icon: 'form',
        routes: [
          {
            name: '池中无品自查工具',
            path: '/tool/filterquery',
            component: './FilterQuery',
          },
          {
            name: '新品手动刷新工具',
            path: '/tool/manualrefresh',
            component: './ManualRefresh',
          },
        ]
      },{
        path: '/otherTool',
        name: '其他工具',
        icon: 'form',
        routes: [
          {
            name: 'excel上传至odps',
            path: '/otherTool/excelUpload',
            component: './ExcelUpload',
          },
        ]
      },{
        path: '/admin',
        name: '管理员',
        icon: 'UserOutlined',
        routes: [
          {
            name: '消息通知',
            icon: 'BellOutlined',
            path: '/admin/messageNotice',
            component: './MessageNotice',
          },
        ]
      },
      {
        component: './404',
      },
    ],
  },
  {
    component: './404',
  },
];
