<img src=https://gw.alipayobjects.com/zos/r/215m6q/20200306202646.png width=200>

## galaxy2

> 星辰2

- 项目类型: 中后台标准 CRUD 模板
- 创建者: 祁南 <<EMAIL>>
- 项目地址: http://gitlab.alibaba-inc.com/trip/galaxy2
- **以上内容通过 clam init one -> 纯前端模版 自动创建**
- 飞猪中后台使用文档可见 [开发手册](https://yuque.antfin-inc.com/zht/book)
- 飞猪中后台技术最佳实践参见 [鲸灵](https://zht.alibaba-inc.com)
- 备注：由于需要接入 DEF 发布，需要确保对应 group 下有 tbfed（前端研发平台）的用户为管理员

### 初始化

1. `<NAME_EMAIL>:trip/galaxy2.git` 克隆仓库
2. `tnpm install` 安装依赖 `npm` 包

## 开发命令

```
$ clam newbranch // 新建分支
$ clam push // 提交代码

$ clam dev // 本地调试

$ clam prepub // 预发
$ clam publish // 正式发布
```
可以经常执行 npm run fix 来修复代码格式问题。

**command+option+control + i** 支持内网环境定位到代码函数，本地调试也是支持的。

## Changelog

### 0.1.0 by 花名

- [+] 新增了 xxx 功能
- [!] 更改了 xxx 逻辑
- [-] 删除了 xxx 逻辑
